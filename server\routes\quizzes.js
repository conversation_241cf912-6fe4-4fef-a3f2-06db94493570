const express = require('express');
const router = express.Router();
const Quiz = require('../models/Quiz');

// Create a new quiz
router.post('/', async (req, res) => {
  try {
    const { question, options, correctAnswer } = req.body;
    
    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({ 
        error: 'Question is required and must have 2-5 options' 
      });
    }

    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
      return res.status(400).json({ 
        error: 'Valid correct answer index is required' 
      });
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      selectedCount: 0
    }));

    const quiz = new Quiz({
      question,
      options: formattedOptions,
      correctAnswer
    });

    const savedQuiz = await quiz.save();
    res.status(201).json(savedQuiz);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get a specific quiz
router.get('/:id', async (req, res) => {
  try {
    const quiz = await Quiz.findById(req.params.id);
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }
    res.json(quiz);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Answer a quiz
router.post('/:id/answer', async (req, res) => {
  try {
    const { optionIndex } = req.body;
    const quiz = await Quiz.findById(req.params.id);
    
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    if (optionIndex < 0 || optionIndex >= quiz.options.length) {
      return res.status(400).json({ error: 'Invalid option index' });
    }

    quiz.options[optionIndex].selectedCount += 1;
    await quiz.save();

    res.json({
      quiz,
      isCorrect: optionIndex === quiz.correctAnswer
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
