const express = require('express');
const router = express.Router();
const Quiz = require('../models/Quiz');
const QRCode = require('qrcode');

// Generate unique codes
function generateCode(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Create a new quiz
router.post('/', async (req, res) => {
  try {
    const { question, options, correctAnswer } = req.body;

    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({
        error: 'Question is required and must have 2-5 options'
      });
    }

    if (correctAnswer === undefined || correctAnswer < 0 || correctAnswer >= options.length) {
      return res.status(400).json({
        error: 'Valid correct answer index is required'
      });
    }

    // Generate unique codes
    let adminCode, participantCode;
    let isUnique = false;

    while (!isUnique) {
      adminCode = generateCode();
      participantCode = generateCode();

      const existingQuiz = await Quiz.findOne({
        $or: [{ adminCode }, { participantCode }]
      });

      if (!existingQuiz) {
        isUnique = true;
      }
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      selectedCount: 0
    }));

    const quiz = new Quiz({
      question,
      options: formattedOptions,
      correctAnswer,
      adminCode,
      participantCode,
      totalAnswers: 0
    });

    const savedQuiz = await quiz.save();

    // Generate QR codes
    const participantUrl = `${req.protocol}://${req.get('host')}/participate/quiz/${participantCode}`;
    const adminUrl = `${req.protocol}://${req.get('host')}/admin/quiz/${adminCode}`;

    const participantQR = await QRCode.toDataURL(participantUrl);
    const adminQR = await QRCode.toDataURL(adminUrl);

    res.status(201).json({
      ...savedQuiz.toObject(),
      participantQR,
      adminQR,
      participantUrl,
      adminUrl
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get quiz by participant code
router.get('/participate/:code', async (req, res) => {
  try {
    const quiz = await Quiz.findOne({ participantCode: req.params.code, isActive: true });
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found or inactive' });
    }
    res.json(quiz);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get quiz by admin code
router.get('/admin/:code', async (req, res) => {
  try {
    const quiz = await Quiz.findOne({ adminCode: req.params.code });
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }
    res.json(quiz);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all quizzes for admin dashboard
router.get('/admin/all/quizzes', async (req, res) => {
  try {
    const quizzes = await Quiz.find().sort({ createdAt: -1 });
    res.json(quizzes);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Answer a quiz
router.post('/participate/:code/answer', async (req, res) => {
  try {
    const { optionIndex } = req.body;
    const quiz = await Quiz.findOne({ participantCode: req.params.code, isActive: true });

    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found or inactive' });
    }

    if (optionIndex < 0 || optionIndex >= quiz.options.length) {
      return res.status(400).json({ error: 'Invalid option index' });
    }

    quiz.options[optionIndex].selectedCount += 1;
    quiz.totalAnswers += 1;
    await quiz.save();

    res.json({
      quiz,
      isCorrect: optionIndex === quiz.correctAnswer
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Toggle quiz status (admin only)
router.patch('/admin/:code/toggle', async (req, res) => {
  try {
    const quiz = await Quiz.findOne({ adminCode: req.params.code });
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }

    quiz.isActive = !quiz.isActive;
    await quiz.save();

    res.json(quiz);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete quiz (admin only)
router.delete('/admin/:code', async (req, res) => {
  try {
    const quiz = await Quiz.findOneAndDelete({ adminCode: req.params.code });
    if (!quiz) {
      return res.status(404).json({ error: 'Quiz not found' });
    }
    res.json({ message: 'Quiz deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
