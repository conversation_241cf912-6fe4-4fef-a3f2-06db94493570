const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const pollRoutes = require('./routes/polls');
const quizRoutes = require('./routes/quizzes');
const Poll = require('./models/Poll');
const Quiz = require('./models/Quiz');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/livepoll_quiz';
mongoose.connect(MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/polls', pollRoutes);
app.use('/api/quizzes', quizRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Join room for specific poll/quiz
  socket.on('join', (data) => {
    const { code, type } = data;
    socket.join(`${type}_${code}`);
    console.log(`User ${socket.id} joined ${type} room: ${code}`);
  });

  // Handle poll voting
  socket.on('vote', async (data) => {
    try {
      const { participantCode, optionIndex } = data;
      const poll = await Poll.findOne({ participantCode, isActive: true });

      if (poll && optionIndex >= 0 && optionIndex < poll.options.length) {
        poll.options[optionIndex].voteCount += 1;
        poll.totalVotes += 1;
        await poll.save();

        // Emit updated results to all clients in the room
        io.to(`poll_${participantCode}`).emit('update', {
          type: 'poll',
          code: participantCode,
          data: poll
        });

        // Also emit to admin room
        io.to(`poll_${poll.adminCode}`).emit('update', {
          type: 'poll',
          code: poll.adminCode,
          data: poll
        });
      }
    } catch (error) {
      console.error('Vote error:', error);
    }
  });

  // Handle quiz answering
  socket.on('answer', async (data) => {
    try {
      const { participantCode, optionIndex } = data;
      const quiz = await Quiz.findOne({ participantCode, isActive: true });

      if (quiz && optionIndex >= 0 && optionIndex < quiz.options.length) {
        quiz.options[optionIndex].selectedCount += 1;
        quiz.totalAnswers += 1;
        await quiz.save();

        // Emit updated results to all clients in the room
        io.to(`quiz_${participantCode}`).emit('update', {
          type: 'quiz',
          code: participantCode,
          data: quiz,
          isCorrect: optionIndex === quiz.correctAnswer
        });

        // Also emit to admin room
        io.to(`quiz_${quiz.adminCode}`).emit('update', {
          type: 'quiz',
          code: quiz.adminCode,
          data: quiz,
          isCorrect: optionIndex === quiz.correctAnswer
        });
      }
    } catch (error) {
      console.error('Answer error:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
