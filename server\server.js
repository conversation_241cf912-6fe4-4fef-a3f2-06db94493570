const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

const pollRoutes = require('./routes/polls');
const quizRoutes = require('./routes/quizzes');
const Poll = require('./models/Poll');
const Quiz = require('./models/Quiz');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/livepoll_quiz';
mongoose.connect(MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Routes
app.use('/api/polls', pollRoutes);
app.use('/api/quizzes', quizRoutes);

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Handle poll voting
  socket.on('vote', async (data) => {
    try {
      const { pollId, optionIndex } = data;
      const poll = await Poll.findById(pollId);
      
      if (poll && optionIndex >= 0 && optionIndex < poll.options.length) {
        poll.options[optionIndex].voteCount += 1;
        await poll.save();
        
        // Emit updated results to all clients
        io.emit('update', {
          type: 'poll',
          id: pollId,
          data: poll
        });
      }
    } catch (error) {
      console.error('Vote error:', error);
    }
  });

  // Handle quiz answering
  socket.on('answer', async (data) => {
    try {
      const { quizId, optionIndex } = data;
      const quiz = await Quiz.findById(quizId);
      
      if (quiz && optionIndex >= 0 && optionIndex < quiz.options.length) {
        quiz.options[optionIndex].selectedCount += 1;
        await quiz.save();
        
        // Emit updated results to all clients
        io.emit('update', {
          type: 'quiz',
          id: quizId,
          data: quiz,
          isCorrect: optionIndex === quiz.correctAnswer
        });
      }
    } catch (error) {
      console.error('Answer error:', error);
    }
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
