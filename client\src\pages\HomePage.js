import React from 'react';
import { Link } from 'react-router-dom';

function HomePage() {
  return (
    <div className="home-page">
      <div className="hero-section">
        <h2>Welcome to Live Poll & Quiz Platform</h2>
        <p>Create interactive polls and quizzes with real-time results and engage your audience like never before!</p>
      </div>

      <div className="features-grid">
        <div className="feature-card">
          <div className="feature-icon">📊</div>
          <h3>Live Polls</h3>
          <p>Create instant polls with up to 5 options and see real-time voting results</p>
          <Link to="/admin/create-poll" className="feature-button">Create Poll</Link>
        </div>

        <div className="feature-card">
          <div className="feature-icon">🧠</div>
          <h3>Interactive Quizzes</h3>
          <p>Build engaging quizzes with correct answers and instant feedback</p>
          <Link to="/admin/create-quiz" className="feature-button">Create Quiz</Link>
        </div>

        <div className="feature-card">
          <div className="feature-icon">📱</div>
          <h3>QR Code Sharing</h3>
          <p>Share polls and quizzes instantly with QR codes for easy access</p>
          <Link to="/admin" className="feature-button">Admin Panel</Link>
        </div>

        <div className="feature-card">
          <div className="feature-icon">⚡</div>
          <h3>Real-time Results</h3>
          <p>Watch responses come in live with beautiful animated charts</p>
          <Link to="/participate" className="feature-button">Join Session</Link>
        </div>
      </div>

      <div className="cta-section">
        <h3>Ready to get started?</h3>
        <div className="cta-buttons">
          <Link to="/admin" className="cta-button primary">Start Creating</Link>
          <Link to="/participate" className="cta-button secondary">Join a Session</Link>
        </div>
      </div>
    </div>
  );
}

export default HomePage;
