{"ast": null, "code": "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, {\n  enumerable: true,\n  configurable: true,\n  writable: true,\n  value\n}) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {})) if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)) {\n    if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);\n  }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source) if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0) target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols) for (var prop of __getOwnPropSymbols(source)) {\n    if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop)) target[prop] = source[prop];\n  }\n  return target;\n};\n\n// src/index.tsx\nimport React from \"react\";\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n(qrcodegen2 => {\n  const _QrCode = class {\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      this.modules = [];\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION) throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7) throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++) row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion;; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion) throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData()) bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17) appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length) dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    getModules() {\n      return this.modules;\n    }\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++) rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++) this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++) this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++) this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++) this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    drawVersion() {\n      if (this.version < 7) return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++) rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++) this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks) dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6) right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    applyMask(mask) {\n      if (mask < 0 || mask > 7) throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5) result += _QrCode.PENALTY_N1;else if (runX > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5) result += _QrCode.PENALTY_N1;else if (runY > 5) result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1]) result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules) dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    getAlignmentPatternPositions() {\n      if (this.version == 1) return [];else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step) result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION) throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7) result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255) throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++) result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length) result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map(_ => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0) currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  let QrCode = _QrCode;\n  QrCode.MIN_VERSION = 1;\n  QrCode.MAX_VERSION = 40;\n  QrCode.PENALTY_N1 = 3;\n  QrCode.PENALTY_N2 = 3;\n  QrCode.PENALTY_N3 = 40;\n  QrCode.PENALTY_N4 = 10;\n  QrCode.ECC_CODEWORDS_PER_BLOCK = [[-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28], [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]];\n  QrCode.NUM_ERROR_CORRECTION_BLOCKS = [[-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25], [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49], [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68], [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]];\n  qrcodegen2.QrCode = QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--) bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond) throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class {\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0) throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data) appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits)) throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length;) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substr(i, n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text)) throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length) appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    static makeSegments(text) {\n      if (text == \"\") return [];else if (_QrSegment.isNumeric(text)) return [_QrSegment.makeNumeric(text)];else if (_QrSegment.isAlphanumeric(text)) return [_QrSegment.makeAlphanumeric(text)];else return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0) throw new RangeError(\"ECI assignment value out of range\");else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    getData() {\n      return this.bitData.slice();\n    }\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits) return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\") result.push(str.charCodeAt(i));else {\n          result.push(parseInt(str.substr(i + 1, 2), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  let QrSegment = _QrSegment;\n  QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  qrcodegen2.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n(qrcodegen2 => {\n  let QrCode;\n  (QrCode2 => {\n    const _Ecc = class {\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    let Ecc = _Ecc;\n    Ecc.LOW = new _Ecc(0, 1);\n    Ecc.MEDIUM = new _Ecc(1, 0);\n    Ecc.QUARTILE = new _Ecc(2, 3);\n    Ecc.HIGH = new _Ecc(3, 2);\n    QrCode2.Ecc = Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n(qrcodegen2 => {\n  let QrSegment;\n  (QrSegment2 => {\n    const _Mode = class {\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    let Mode = _Mode;\n    Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    Mode.ECI = new _Mode(7, [0, 0, 0]);\n    QrSegment2.Mode = Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar MARGIN_SIZE = 4;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function (row, y) {\n    let start = null;\n    row.forEach(function (cell, x) {\n      if (!cell && start !== null) {\n        ops.push(`M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`);\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(`M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`);\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, includeMargin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = {\n      x: floorX,\n      y: floorY,\n      w: ceilW,\n      h: ceilH\n    };\n  }\n  return {\n    x,\n    y,\n    h,\n    w,\n    excavation\n  };\n}\nvar SUPPORTS_PATH2D = function () {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nfunction QRCodeCanvas(props) {\n  const _a = props,\n    {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      style,\n      imageSettings\n    } = _a,\n    otherProps = __objRest(_a, [\"value\", \"size\", \"level\", \"bgColor\", \"fgColor\", \"includeMargin\", \"style\", \"imageSettings\"]);\n  const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n  const _canvas = React.useRef(null);\n  const _image = React.useRef(null);\n  const [isImgLoaded, setIsImageLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (_canvas.current != null) {\n      const canvas = _canvas.current;\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) {\n        return;\n      }\n      let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n      const margin = includeMargin ? MARGIN_SIZE : 0;\n      const numCells = cells.length + margin * 2;\n      const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n      const image = _image.current;\n      const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n      if (haveImageToRender) {\n        if (calculatedImageSettings.excavation != null) {\n          cells = excavateModules(cells, calculatedImageSettings.excavation);\n        }\n      }\n      const pixelRatio = window.devicePixelRatio || 1;\n      canvas.height = canvas.width = size * pixelRatio;\n      const scale = size / numCells * pixelRatio;\n      ctx.scale(scale, scale);\n      ctx.fillStyle = bgColor;\n      ctx.fillRect(0, 0, numCells, numCells);\n      ctx.fillStyle = fgColor;\n      if (SUPPORTS_PATH2D) {\n        ctx.fill(new Path2D(generatePath(cells, margin)));\n      } else {\n        cells.forEach(function (row, rdx) {\n          row.forEach(function (cell, cdx) {\n            if (cell) {\n              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n            }\n          });\n        });\n      }\n      if (haveImageToRender) {\n        ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);\n      }\n    }\n  });\n  React.useEffect(() => {\n    setIsImageLoaded(false);\n  }, [imgSrc]);\n  const canvasStyle = __spreadValues({\n    height: size,\n    width: size\n  }, style);\n  let img = null;\n  if (imgSrc != null) {\n    img = /* @__PURE__ */React.createElement(\"img\", {\n      src: imgSrc,\n      key: imgSrc,\n      style: {\n        display: \"none\"\n      },\n      onLoad: () => {\n        setIsImageLoaded(true);\n      },\n      ref: _image\n    });\n  }\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(\"canvas\", __spreadValues({\n    style: canvasStyle,\n    height: size,\n    width: size,\n    ref: _canvas\n  }, otherProps)), img);\n}\nfunction QRCodeSVG(props) {\n  const _a = props,\n    {\n      value,\n      size = DEFAULT_SIZE,\n      level = DEFAULT_LEVEL,\n      bgColor = DEFAULT_BGCOLOR,\n      fgColor = DEFAULT_FGCOLOR,\n      includeMargin = DEFAULT_INCLUDEMARGIN,\n      imageSettings\n    } = _a,\n    otherProps = __objRest(_a, [\"value\", \"size\", \"level\", \"bgColor\", \"fgColor\", \"includeMargin\", \"imageSettings\"]);\n  let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n  let image = null;\n  if (imageSettings != null && calculatedImageSettings != null) {\n    if (calculatedImageSettings.excavation != null) {\n      cells = excavateModules(cells, calculatedImageSettings.excavation);\n    }\n    image = /* @__PURE__ */React.createElement(\"image\", {\n      xlinkHref: imageSettings.src,\n      height: calculatedImageSettings.h,\n      width: calculatedImageSettings.w,\n      x: calculatedImageSettings.x + margin,\n      y: calculatedImageSettings.y + margin,\n      preserveAspectRatio: \"none\"\n    });\n  }\n  const fgPath = generatePath(cells, margin);\n  return /* @__PURE__ */React.createElement(\"svg\", __spreadValues({\n    height: size,\n    width: size,\n    viewBox: `0 0 ${numCells} ${numCells}`\n  }, otherProps), /* @__PURE__ */React.createElement(\"path\", {\n    fill: bgColor,\n    d: `M0,0 h${numCells}v${numCells}H0z`,\n    shapeRendering: \"crispEdges\"\n  }), /* @__PURE__ */React.createElement(\"path\", {\n    fill: fgColor,\n    d: fgPath,\n    shapeRendering: \"crispEdges\"\n  }), image);\n}\nvar QRCode = props => {\n  const _a = props,\n    {\n      renderAs\n    } = _a,\n    otherProps = __objRest(_a, [\"renderAs\"]);\n  if (renderAs === \"svg\") {\n    return /* @__PURE__ */React.createElement(QRCodeSVG, __spreadValues({}, otherProps));\n  }\n  return /* @__PURE__ */React.createElement(QRCodeCanvas, __spreadValues({}, otherProps));\n};\nexport { QRCodeCanvas, QRCodeSVG, QRCode as default };", "map": {"version": 3, "names": ["__defProp", "Object", "defineProperty", "__getOwnPropSymbols", "getOwnPropertySymbols", "__hasOwnProp", "prototype", "hasOwnProperty", "__propIsEnum", "propertyIsEnumerable", "__defNormalProp", "obj", "key", "value", "enumerable", "configurable", "writable", "__spreadValues", "a", "b", "prop", "call", "__objRest", "source", "exclude", "target", "indexOf", "React", "qrcodegen", "qrcodegen2", "_QrCode", "constructor", "version", "errorCorrectionLevel", "dataCodewords", "msk", "modules", "isFunction", "MIN_VERSION", "MAX_VERSION", "RangeError", "size", "row", "i", "push", "slice", "drawFunctionPatterns", "allCodewords", "addEccAndInterleave", "drawCodewords", "min<PERSON><PERSON><PERSON><PERSON>", "applyMask", "drawFormatBits", "penalty", "getPenaltyScore", "assert", "mask", "encodeText", "text", "ecl", "segs", "QrSegment", "makeSegments", "encodeSegments", "encodeBinary", "data", "seg", "makeBytes", "minVersion", "maxVersion", "boostEcl", "dataUsedBits", "dataCapacityBits2", "getNumDataCodewords", "usedBits", "getTotalBits", "newEcl", "Ecc", "MEDIUM", "QUARTILE", "HIGH", "bb", "appendBits", "mode", "modeBits", "numChars", "numCharCountBits", "getData", "length", "dataCapacityBits", "Math", "min", "padByte", "for<PERSON>ach", "getModule", "x", "y", "getModules", "setFunctionModule", "drawFinderPattern", "alignPatPos", "getAlignmentPatternPositions", "numAlign", "j", "drawAlignmentPattern", "drawVersion", "formatBits", "rem", "bits", "getBit", "color", "floor", "dy", "dx", "dist", "max", "abs", "xx", "yy", "isDark", "ver", "numBlocks", "NUM_ERROR_CORRECTION_BLOCKS", "ordinal", "blockEccLen", "ECC_CODEWORDS_PER_BLOCK", "rawCodewords", "getNumRawDataModules", "numShortBlocks", "shortBlockLen", "blocks", "rsDiv", "reedSolomonComputeDivisor", "k", "dat", "ecc", "reedSolomonComputeRemainder", "concat", "result", "block", "right", "vert", "upward", "invert", "Error", "runColor", "runX", "runHistory", "PENALTY_N1", "finderPenaltyAddHistory", "finderPenaltyCountPatterns", "PENALTY_N3", "finderPenaltyTerminateAndCount", "runY", "PENALTY_N2", "dark", "reduce", "sum", "total", "ceil", "PENALTY_N4", "step", "pos", "splice", "degree", "root", "reedSolomonMultiply", "divisor", "map", "_", "factor", "shift", "coef", "z", "n", "core", "currentRunColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pop", "unshift", "QrCode", "val", "len", "cond", "_QrSegment", "bitData", "Mode", "BYTE", "makeNumeric", "digits", "isNumeric", "parseInt", "substr", "NUMERIC", "makeAlphanumeric", "isAlphanumeric", "temp", "ALPHANUMERIC_CHARSET", "char<PERSON>t", "ALPHANUMERIC", "toUtf8ByteArray", "makeEci", "assignVal", "ECI", "NUMERIC_REGEX", "test", "ALPHANUMERIC_REGEX", "ccbits", "Infinity", "str", "encodeURI", "charCodeAt", "QrCode2", "_Ecc", "LOW", "QrSegment2", "_Mode", "numBitsCharCount", "KANJI", "qrcodegen_default", "ERROR_LEVEL_MAP", "L", "M", "Q", "H", "DEFAULT_SIZE", "DEFAULT_LEVEL", "DEFAULT_BGCOLOR", "DEFAULT_FGCOLOR", "DEFAULT_INCLUDEMARGIN", "MARGIN_SIZE", "DEFAULT_IMG_SCALE", "generatePath", "margin", "ops", "start", "cell", "join", "excavateModules", "excavation", "h", "w", "getImageSettings", "cells", "<PERSON><PERSON><PERSON><PERSON>", "imageSettings", "num<PERSON>ells", "defaultSize", "scale", "width", "height", "excavate", "floorX", "floorY", "ceilW", "ceilH", "SUPPORTS_PATH2D", "Path2D", "addPath", "e", "QRCodeCanvas", "props", "_a", "level", "bgColor", "fgColor", "style", "otherProps", "imgSrc", "src", "_canvas", "useRef", "_image", "isImgLoaded", "setIsImageLoaded", "useState", "useEffect", "current", "canvas", "ctx", "getContext", "calculatedImageSettings", "image", "haveImageToRender", "complete", "naturalHeight", "naturalWidth", "pixelRatio", "window", "devicePixelRatio", "fillStyle", "fillRect", "fill", "rdx", "cdx", "drawImage", "canvasStyle", "img", "createElement", "display", "onLoad", "ref", "Fragment", "QRCodeSVG", "xlinkHref", "preserveAspectRatio", "fgPath", "viewBox", "d", "shapeRendering", "QRCode", "renderAs", "default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/node_modules/qrcode.react/lib/esm/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/index.tsx\nimport React from \"react\";\n\n// src/third-party/qrcodegen/index.ts\n/**\n * @license QR Code generator library (TypeScript)\n * Copyright (c) Project Nayuki.\n * SPDX-License-Identifier: MIT\n */\nvar qrcodegen;\n((qrcodegen2) => {\n  const _QrCode = class {\n    constructor(version, errorCorrectionLevel, dataCodewords, msk) {\n      this.version = version;\n      this.errorCorrectionLevel = errorCorrectionLevel;\n      this.modules = [];\n      this.isFunction = [];\n      if (version < _QrCode.MIN_VERSION || version > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version value out of range\");\n      if (msk < -1 || msk > 7)\n        throw new RangeError(\"Mask value out of range\");\n      this.size = version * 4 + 17;\n      let row = [];\n      for (let i = 0; i < this.size; i++)\n        row.push(false);\n      for (let i = 0; i < this.size; i++) {\n        this.modules.push(row.slice());\n        this.isFunction.push(row.slice());\n      }\n      this.drawFunctionPatterns();\n      const allCodewords = this.addEccAndInterleave(dataCodewords);\n      this.drawCodewords(allCodewords);\n      if (msk == -1) {\n        let minPenalty = 1e9;\n        for (let i = 0; i < 8; i++) {\n          this.applyMask(i);\n          this.drawFormatBits(i);\n          const penalty = this.getPenaltyScore();\n          if (penalty < minPenalty) {\n            msk = i;\n            minPenalty = penalty;\n          }\n          this.applyMask(i);\n        }\n      }\n      assert(0 <= msk && msk <= 7);\n      this.mask = msk;\n      this.applyMask(msk);\n      this.drawFormatBits(msk);\n      this.isFunction = [];\n    }\n    static encodeText(text, ecl) {\n      const segs = qrcodegen2.QrSegment.makeSegments(text);\n      return _QrCode.encodeSegments(segs, ecl);\n    }\n    static encodeBinary(data, ecl) {\n      const seg = qrcodegen2.QrSegment.makeBytes(data);\n      return _QrCode.encodeSegments([seg], ecl);\n    }\n    static encodeSegments(segs, ecl, minVersion = 1, maxVersion = 40, mask = -1, boostEcl = true) {\n      if (!(_QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= _QrCode.MAX_VERSION) || mask < -1 || mask > 7)\n        throw new RangeError(\"Invalid value\");\n      let version;\n      let dataUsedBits;\n      for (version = minVersion; ; version++) {\n        const dataCapacityBits2 = _QrCode.getNumDataCodewords(version, ecl) * 8;\n        const usedBits = QrSegment.getTotalBits(segs, version);\n        if (usedBits <= dataCapacityBits2) {\n          dataUsedBits = usedBits;\n          break;\n        }\n        if (version >= maxVersion)\n          throw new RangeError(\"Data too long\");\n      }\n      for (const newEcl of [_QrCode.Ecc.MEDIUM, _QrCode.Ecc.QUARTILE, _QrCode.Ecc.HIGH]) {\n        if (boostEcl && dataUsedBits <= _QrCode.getNumDataCodewords(version, newEcl) * 8)\n          ecl = newEcl;\n      }\n      let bb = [];\n      for (const seg of segs) {\n        appendBits(seg.mode.modeBits, 4, bb);\n        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);\n        for (const b of seg.getData())\n          bb.push(b);\n      }\n      assert(bb.length == dataUsedBits);\n      const dataCapacityBits = _QrCode.getNumDataCodewords(version, ecl) * 8;\n      assert(bb.length <= dataCapacityBits);\n      appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);\n      appendBits(0, (8 - bb.length % 8) % 8, bb);\n      assert(bb.length % 8 == 0);\n      for (let padByte = 236; bb.length < dataCapacityBits; padByte ^= 236 ^ 17)\n        appendBits(padByte, 8, bb);\n      let dataCodewords = [];\n      while (dataCodewords.length * 8 < bb.length)\n        dataCodewords.push(0);\n      bb.forEach((b, i) => dataCodewords[i >>> 3] |= b << 7 - (i & 7));\n      return new _QrCode(version, ecl, dataCodewords, mask);\n    }\n    getModule(x, y) {\n      return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];\n    }\n    getModules() {\n      return this.modules;\n    }\n    drawFunctionPatterns() {\n      for (let i = 0; i < this.size; i++) {\n        this.setFunctionModule(6, i, i % 2 == 0);\n        this.setFunctionModule(i, 6, i % 2 == 0);\n      }\n      this.drawFinderPattern(3, 3);\n      this.drawFinderPattern(this.size - 4, 3);\n      this.drawFinderPattern(3, this.size - 4);\n      const alignPatPos = this.getAlignmentPatternPositions();\n      const numAlign = alignPatPos.length;\n      for (let i = 0; i < numAlign; i++) {\n        for (let j = 0; j < numAlign; j++) {\n          if (!(i == 0 && j == 0 || i == 0 && j == numAlign - 1 || i == numAlign - 1 && j == 0))\n            this.drawAlignmentPattern(alignPatPos[i], alignPatPos[j]);\n        }\n      }\n      this.drawFormatBits(0);\n      this.drawVersion();\n    }\n    drawFormatBits(mask) {\n      const data = this.errorCorrectionLevel.formatBits << 3 | mask;\n      let rem = data;\n      for (let i = 0; i < 10; i++)\n        rem = rem << 1 ^ (rem >>> 9) * 1335;\n      const bits = (data << 10 | rem) ^ 21522;\n      assert(bits >>> 15 == 0);\n      for (let i = 0; i <= 5; i++)\n        this.setFunctionModule(8, i, getBit(bits, i));\n      this.setFunctionModule(8, 7, getBit(bits, 6));\n      this.setFunctionModule(8, 8, getBit(bits, 7));\n      this.setFunctionModule(7, 8, getBit(bits, 8));\n      for (let i = 9; i < 15; i++)\n        this.setFunctionModule(14 - i, 8, getBit(bits, i));\n      for (let i = 0; i < 8; i++)\n        this.setFunctionModule(this.size - 1 - i, 8, getBit(bits, i));\n      for (let i = 8; i < 15; i++)\n        this.setFunctionModule(8, this.size - 15 + i, getBit(bits, i));\n      this.setFunctionModule(8, this.size - 8, true);\n    }\n    drawVersion() {\n      if (this.version < 7)\n        return;\n      let rem = this.version;\n      for (let i = 0; i < 12; i++)\n        rem = rem << 1 ^ (rem >>> 11) * 7973;\n      const bits = this.version << 12 | rem;\n      assert(bits >>> 18 == 0);\n      for (let i = 0; i < 18; i++) {\n        const color = getBit(bits, i);\n        const a = this.size - 11 + i % 3;\n        const b = Math.floor(i / 3);\n        this.setFunctionModule(a, b, color);\n        this.setFunctionModule(b, a, color);\n      }\n    }\n    drawFinderPattern(x, y) {\n      for (let dy = -4; dy <= 4; dy++) {\n        for (let dx = -4; dx <= 4; dx++) {\n          const dist = Math.max(Math.abs(dx), Math.abs(dy));\n          const xx = x + dx;\n          const yy = y + dy;\n          if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size)\n            this.setFunctionModule(xx, yy, dist != 2 && dist != 4);\n        }\n      }\n    }\n    drawAlignmentPattern(x, y) {\n      for (let dy = -2; dy <= 2; dy++) {\n        for (let dx = -2; dx <= 2; dx++)\n          this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);\n      }\n    }\n    setFunctionModule(x, y, isDark) {\n      this.modules[y][x] = isDark;\n      this.isFunction[y][x] = true;\n    }\n    addEccAndInterleave(data) {\n      const ver = this.version;\n      const ecl = this.errorCorrectionLevel;\n      if (data.length != _QrCode.getNumDataCodewords(ver, ecl))\n        throw new RangeError(\"Invalid argument\");\n      const numBlocks = _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n      const blockEccLen = _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];\n      const rawCodewords = Math.floor(_QrCode.getNumRawDataModules(ver) / 8);\n      const numShortBlocks = numBlocks - rawCodewords % numBlocks;\n      const shortBlockLen = Math.floor(rawCodewords / numBlocks);\n      let blocks = [];\n      const rsDiv = _QrCode.reedSolomonComputeDivisor(blockEccLen);\n      for (let i = 0, k = 0; i < numBlocks; i++) {\n        let dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));\n        k += dat.length;\n        const ecc = _QrCode.reedSolomonComputeRemainder(dat, rsDiv);\n        if (i < numShortBlocks)\n          dat.push(0);\n        blocks.push(dat.concat(ecc));\n      }\n      let result = [];\n      for (let i = 0; i < blocks[0].length; i++) {\n        blocks.forEach((block, j) => {\n          if (i != shortBlockLen - blockEccLen || j >= numShortBlocks)\n            result.push(block[i]);\n        });\n      }\n      assert(result.length == rawCodewords);\n      return result;\n    }\n    drawCodewords(data) {\n      if (data.length != Math.floor(_QrCode.getNumRawDataModules(this.version) / 8))\n        throw new RangeError(\"Invalid argument\");\n      let i = 0;\n      for (let right = this.size - 1; right >= 1; right -= 2) {\n        if (right == 6)\n          right = 5;\n        for (let vert = 0; vert < this.size; vert++) {\n          for (let j = 0; j < 2; j++) {\n            const x = right - j;\n            const upward = (right + 1 & 2) == 0;\n            const y = upward ? this.size - 1 - vert : vert;\n            if (!this.isFunction[y][x] && i < data.length * 8) {\n              this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));\n              i++;\n            }\n          }\n        }\n      }\n      assert(i == data.length * 8);\n    }\n    applyMask(mask) {\n      if (mask < 0 || mask > 7)\n        throw new RangeError(\"Mask value out of range\");\n      for (let y = 0; y < this.size; y++) {\n        for (let x = 0; x < this.size; x++) {\n          let invert;\n          switch (mask) {\n            case 0:\n              invert = (x + y) % 2 == 0;\n              break;\n            case 1:\n              invert = y % 2 == 0;\n              break;\n            case 2:\n              invert = x % 3 == 0;\n              break;\n            case 3:\n              invert = (x + y) % 3 == 0;\n              break;\n            case 4:\n              invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;\n              break;\n            case 5:\n              invert = x * y % 2 + x * y % 3 == 0;\n              break;\n            case 6:\n              invert = (x * y % 2 + x * y % 3) % 2 == 0;\n              break;\n            case 7:\n              invert = ((x + y) % 2 + x * y % 3) % 2 == 0;\n              break;\n            default:\n              throw new Error(\"Unreachable\");\n          }\n          if (!this.isFunction[y][x] && invert)\n            this.modules[y][x] = !this.modules[y][x];\n        }\n      }\n    }\n    getPenaltyScore() {\n      let result = 0;\n      for (let y = 0; y < this.size; y++) {\n        let runColor = false;\n        let runX = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let x = 0; x < this.size; x++) {\n          if (this.modules[y][x] == runColor) {\n            runX++;\n            if (runX == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runX > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runX, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runX = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let x = 0; x < this.size; x++) {\n        let runColor = false;\n        let runY = 0;\n        let runHistory = [0, 0, 0, 0, 0, 0, 0];\n        for (let y = 0; y < this.size; y++) {\n          if (this.modules[y][x] == runColor) {\n            runY++;\n            if (runY == 5)\n              result += _QrCode.PENALTY_N1;\n            else if (runY > 5)\n              result++;\n          } else {\n            this.finderPenaltyAddHistory(runY, runHistory);\n            if (!runColor)\n              result += this.finderPenaltyCountPatterns(runHistory) * _QrCode.PENALTY_N3;\n            runColor = this.modules[y][x];\n            runY = 1;\n          }\n        }\n        result += this.finderPenaltyTerminateAndCount(runColor, runY, runHistory) * _QrCode.PENALTY_N3;\n      }\n      for (let y = 0; y < this.size - 1; y++) {\n        for (let x = 0; x < this.size - 1; x++) {\n          const color = this.modules[y][x];\n          if (color == this.modules[y][x + 1] && color == this.modules[y + 1][x] && color == this.modules[y + 1][x + 1])\n            result += _QrCode.PENALTY_N2;\n        }\n      }\n      let dark = 0;\n      for (const row of this.modules)\n        dark = row.reduce((sum, color) => sum + (color ? 1 : 0), dark);\n      const total = this.size * this.size;\n      const k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;\n      assert(0 <= k && k <= 9);\n      result += k * _QrCode.PENALTY_N4;\n      assert(0 <= result && result <= 2568888);\n      return result;\n    }\n    getAlignmentPatternPositions() {\n      if (this.version == 1)\n        return [];\n      else {\n        const numAlign = Math.floor(this.version / 7) + 2;\n        const step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;\n        let result = [6];\n        for (let pos = this.size - 7; result.length < numAlign; pos -= step)\n          result.splice(1, 0, pos);\n        return result;\n      }\n    }\n    static getNumRawDataModules(ver) {\n      if (ver < _QrCode.MIN_VERSION || ver > _QrCode.MAX_VERSION)\n        throw new RangeError(\"Version number out of range\");\n      let result = (16 * ver + 128) * ver + 64;\n      if (ver >= 2) {\n        const numAlign = Math.floor(ver / 7) + 2;\n        result -= (25 * numAlign - 10) * numAlign - 55;\n        if (ver >= 7)\n          result -= 36;\n      }\n      assert(208 <= result && result <= 29648);\n      return result;\n    }\n    static getNumDataCodewords(ver, ecl) {\n      return Math.floor(_QrCode.getNumRawDataModules(ver) / 8) - _QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * _QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];\n    }\n    static reedSolomonComputeDivisor(degree) {\n      if (degree < 1 || degree > 255)\n        throw new RangeError(\"Degree out of range\");\n      let result = [];\n      for (let i = 0; i < degree - 1; i++)\n        result.push(0);\n      result.push(1);\n      let root = 1;\n      for (let i = 0; i < degree; i++) {\n        for (let j = 0; j < result.length; j++) {\n          result[j] = _QrCode.reedSolomonMultiply(result[j], root);\n          if (j + 1 < result.length)\n            result[j] ^= result[j + 1];\n        }\n        root = _QrCode.reedSolomonMultiply(root, 2);\n      }\n      return result;\n    }\n    static reedSolomonComputeRemainder(data, divisor) {\n      let result = divisor.map((_) => 0);\n      for (const b of data) {\n        const factor = b ^ result.shift();\n        result.push(0);\n        divisor.forEach((coef, i) => result[i] ^= _QrCode.reedSolomonMultiply(coef, factor));\n      }\n      return result;\n    }\n    static reedSolomonMultiply(x, y) {\n      if (x >>> 8 != 0 || y >>> 8 != 0)\n        throw new RangeError(\"Byte out of range\");\n      let z = 0;\n      for (let i = 7; i >= 0; i--) {\n        z = z << 1 ^ (z >>> 7) * 285;\n        z ^= (y >>> i & 1) * x;\n      }\n      assert(z >>> 8 == 0);\n      return z;\n    }\n    finderPenaltyCountPatterns(runHistory) {\n      const n = runHistory[1];\n      assert(n <= this.size * 3);\n      const core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;\n      return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);\n    }\n    finderPenaltyTerminateAndCount(currentRunColor, currentRunLength, runHistory) {\n      if (currentRunColor) {\n        this.finderPenaltyAddHistory(currentRunLength, runHistory);\n        currentRunLength = 0;\n      }\n      currentRunLength += this.size;\n      this.finderPenaltyAddHistory(currentRunLength, runHistory);\n      return this.finderPenaltyCountPatterns(runHistory);\n    }\n    finderPenaltyAddHistory(currentRunLength, runHistory) {\n      if (runHistory[0] == 0)\n        currentRunLength += this.size;\n      runHistory.pop();\n      runHistory.unshift(currentRunLength);\n    }\n  };\n  let QrCode = _QrCode;\n  QrCode.MIN_VERSION = 1;\n  QrCode.MAX_VERSION = 40;\n  QrCode.PENALTY_N1 = 3;\n  QrCode.PENALTY_N2 = 3;\n  QrCode.PENALTY_N3 = 40;\n  QrCode.PENALTY_N4 = 10;\n  QrCode.ECC_CODEWORDS_PER_BLOCK = [\n    [-1, 7, 10, 15, 20, 26, 18, 20, 24, 30, 18, 20, 24, 26, 30, 22, 24, 28, 30, 28, 28, 28, 28, 30, 30, 26, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    [-1, 10, 16, 26, 18, 24, 16, 18, 22, 22, 26, 30, 22, 22, 24, 24, 28, 28, 26, 26, 26, 26, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28, 28],\n    [-1, 13, 22, 18, 26, 18, 24, 18, 22, 20, 24, 28, 26, 24, 20, 30, 24, 28, 28, 26, 30, 28, 30, 30, 30, 30, 28, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30],\n    [-1, 17, 28, 22, 16, 22, 28, 26, 26, 24, 28, 24, 28, 22, 24, 24, 30, 28, 28, 26, 28, 30, 24, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30]\n  ];\n  QrCode.NUM_ERROR_CORRECTION_BLOCKS = [\n    [-1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 4, 4, 4, 4, 4, 6, 6, 6, 6, 7, 8, 8, 9, 9, 10, 12, 12, 12, 13, 14, 15, 16, 17, 18, 19, 19, 20, 21, 22, 24, 25],\n    [-1, 1, 1, 1, 2, 2, 4, 4, 4, 5, 5, 5, 8, 9, 9, 10, 10, 11, 13, 14, 16, 17, 17, 18, 20, 21, 23, 25, 26, 28, 29, 31, 33, 35, 37, 38, 40, 43, 45, 47, 49],\n    [-1, 1, 1, 2, 2, 4, 4, 6, 6, 8, 8, 8, 10, 12, 16, 12, 17, 16, 18, 21, 20, 23, 23, 25, 27, 29, 34, 34, 35, 38, 40, 43, 45, 48, 51, 53, 56, 59, 62, 65, 68],\n    [-1, 1, 1, 2, 4, 4, 4, 5, 6, 8, 8, 11, 11, 16, 16, 18, 16, 19, 21, 25, 25, 25, 34, 30, 32, 35, 37, 40, 42, 45, 48, 51, 54, 57, 60, 63, 66, 70, 74, 77, 81]\n  ];\n  qrcodegen2.QrCode = QrCode;\n  function appendBits(val, len, bb) {\n    if (len < 0 || len > 31 || val >>> len != 0)\n      throw new RangeError(\"Value out of range\");\n    for (let i = len - 1; i >= 0; i--)\n      bb.push(val >>> i & 1);\n  }\n  function getBit(x, i) {\n    return (x >>> i & 1) != 0;\n  }\n  function assert(cond) {\n    if (!cond)\n      throw new Error(\"Assertion error\");\n  }\n  const _QrSegment = class {\n    constructor(mode, numChars, bitData) {\n      this.mode = mode;\n      this.numChars = numChars;\n      this.bitData = bitData;\n      if (numChars < 0)\n        throw new RangeError(\"Invalid argument\");\n      this.bitData = bitData.slice();\n    }\n    static makeBytes(data) {\n      let bb = [];\n      for (const b of data)\n        appendBits(b, 8, bb);\n      return new _QrSegment(_QrSegment.Mode.BYTE, data.length, bb);\n    }\n    static makeNumeric(digits) {\n      if (!_QrSegment.isNumeric(digits))\n        throw new RangeError(\"String contains non-numeric characters\");\n      let bb = [];\n      for (let i = 0; i < digits.length; ) {\n        const n = Math.min(digits.length - i, 3);\n        appendBits(parseInt(digits.substr(i, n), 10), n * 3 + 1, bb);\n        i += n;\n      }\n      return new _QrSegment(_QrSegment.Mode.NUMERIC, digits.length, bb);\n    }\n    static makeAlphanumeric(text) {\n      if (!_QrSegment.isAlphanumeric(text))\n        throw new RangeError(\"String contains unencodable characters in alphanumeric mode\");\n      let bb = [];\n      let i;\n      for (i = 0; i + 2 <= text.length; i += 2) {\n        let temp = _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;\n        temp += _QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));\n        appendBits(temp, 11, bb);\n      }\n      if (i < text.length)\n        appendBits(_QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);\n      return new _QrSegment(_QrSegment.Mode.ALPHANUMERIC, text.length, bb);\n    }\n    static makeSegments(text) {\n      if (text == \"\")\n        return [];\n      else if (_QrSegment.isNumeric(text))\n        return [_QrSegment.makeNumeric(text)];\n      else if (_QrSegment.isAlphanumeric(text))\n        return [_QrSegment.makeAlphanumeric(text)];\n      else\n        return [_QrSegment.makeBytes(_QrSegment.toUtf8ByteArray(text))];\n    }\n    static makeEci(assignVal) {\n      let bb = [];\n      if (assignVal < 0)\n        throw new RangeError(\"ECI assignment value out of range\");\n      else if (assignVal < 1 << 7)\n        appendBits(assignVal, 8, bb);\n      else if (assignVal < 1 << 14) {\n        appendBits(2, 2, bb);\n        appendBits(assignVal, 14, bb);\n      } else if (assignVal < 1e6) {\n        appendBits(6, 3, bb);\n        appendBits(assignVal, 21, bb);\n      } else\n        throw new RangeError(\"ECI assignment value out of range\");\n      return new _QrSegment(_QrSegment.Mode.ECI, 0, bb);\n    }\n    static isNumeric(text) {\n      return _QrSegment.NUMERIC_REGEX.test(text);\n    }\n    static isAlphanumeric(text) {\n      return _QrSegment.ALPHANUMERIC_REGEX.test(text);\n    }\n    getData() {\n      return this.bitData.slice();\n    }\n    static getTotalBits(segs, version) {\n      let result = 0;\n      for (const seg of segs) {\n        const ccbits = seg.mode.numCharCountBits(version);\n        if (seg.numChars >= 1 << ccbits)\n          return Infinity;\n        result += 4 + ccbits + seg.bitData.length;\n      }\n      return result;\n    }\n    static toUtf8ByteArray(str) {\n      str = encodeURI(str);\n      let result = [];\n      for (let i = 0; i < str.length; i++) {\n        if (str.charAt(i) != \"%\")\n          result.push(str.charCodeAt(i));\n        else {\n          result.push(parseInt(str.substr(i + 1, 2), 16));\n          i += 2;\n        }\n      }\n      return result;\n    }\n  };\n  let QrSegment = _QrSegment;\n  QrSegment.NUMERIC_REGEX = /^[0-9]*$/;\n  QrSegment.ALPHANUMERIC_REGEX = /^[A-Z0-9 $%*+.\\/:-]*$/;\n  QrSegment.ALPHANUMERIC_CHARSET = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:\";\n  qrcodegen2.QrSegment = QrSegment;\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrCode;\n  ((QrCode2) => {\n    const _Ecc = class {\n      constructor(ordinal, formatBits) {\n        this.ordinal = ordinal;\n        this.formatBits = formatBits;\n      }\n    };\n    let Ecc = _Ecc;\n    Ecc.LOW = new _Ecc(0, 1);\n    Ecc.MEDIUM = new _Ecc(1, 0);\n    Ecc.QUARTILE = new _Ecc(2, 3);\n    Ecc.HIGH = new _Ecc(3, 2);\n    QrCode2.Ecc = Ecc;\n  })(QrCode = qrcodegen2.QrCode || (qrcodegen2.QrCode = {}));\n})(qrcodegen || (qrcodegen = {}));\n((qrcodegen2) => {\n  let QrSegment;\n  ((QrSegment2) => {\n    const _Mode = class {\n      constructor(modeBits, numBitsCharCount) {\n        this.modeBits = modeBits;\n        this.numBitsCharCount = numBitsCharCount;\n      }\n      numCharCountBits(ver) {\n        return this.numBitsCharCount[Math.floor((ver + 7) / 17)];\n      }\n    };\n    let Mode = _Mode;\n    Mode.NUMERIC = new _Mode(1, [10, 12, 14]);\n    Mode.ALPHANUMERIC = new _Mode(2, [9, 11, 13]);\n    Mode.BYTE = new _Mode(4, [8, 16, 16]);\n    Mode.KANJI = new _Mode(8, [8, 10, 12]);\n    Mode.ECI = new _Mode(7, [0, 0, 0]);\n    QrSegment2.Mode = Mode;\n  })(QrSegment = qrcodegen2.QrSegment || (qrcodegen2.QrSegment = {}));\n})(qrcodegen || (qrcodegen = {}));\nvar qrcodegen_default = qrcodegen;\n\n// src/index.tsx\n/**\n * @license qrcode.react\n * Copyright (c) Paul O'Shannessy\n * SPDX-License-Identifier: ISC\n */\nvar ERROR_LEVEL_MAP = {\n  L: qrcodegen_default.QrCode.Ecc.LOW,\n  M: qrcodegen_default.QrCode.Ecc.MEDIUM,\n  Q: qrcodegen_default.QrCode.Ecc.QUARTILE,\n  H: qrcodegen_default.QrCode.Ecc.HIGH\n};\nvar DEFAULT_SIZE = 128;\nvar DEFAULT_LEVEL = \"L\";\nvar DEFAULT_BGCOLOR = \"#FFFFFF\";\nvar DEFAULT_FGCOLOR = \"#000000\";\nvar DEFAULT_INCLUDEMARGIN = false;\nvar MARGIN_SIZE = 4;\nvar DEFAULT_IMG_SCALE = 0.1;\nfunction generatePath(modules, margin = 0) {\n  const ops = [];\n  modules.forEach(function(row, y) {\n    let start = null;\n    row.forEach(function(cell, x) {\n      if (!cell && start !== null) {\n        ops.push(`M${start + margin} ${y + margin}h${x - start}v1H${start + margin}z`);\n        start = null;\n        return;\n      }\n      if (x === row.length - 1) {\n        if (!cell) {\n          return;\n        }\n        if (start === null) {\n          ops.push(`M${x + margin},${y + margin} h1v1H${x + margin}z`);\n        } else {\n          ops.push(`M${start + margin},${y + margin} h${x + 1 - start}v1H${start + margin}z`);\n        }\n        return;\n      }\n      if (cell && start === null) {\n        start = x;\n      }\n    });\n  });\n  return ops.join(\"\");\n}\nfunction excavateModules(modules, excavation) {\n  return modules.slice().map((row, y) => {\n    if (y < excavation.y || y >= excavation.y + excavation.h) {\n      return row;\n    }\n    return row.map((cell, x) => {\n      if (x < excavation.x || x >= excavation.x + excavation.w) {\n        return cell;\n      }\n      return false;\n    });\n  });\n}\nfunction getImageSettings(cells, size, includeMargin, imageSettings) {\n  if (imageSettings == null) {\n    return null;\n  }\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);\n  const scale = numCells / size;\n  const w = (imageSettings.width || defaultSize) * scale;\n  const h = (imageSettings.height || defaultSize) * scale;\n  const x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;\n  const y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;\n  let excavation = null;\n  if (imageSettings.excavate) {\n    let floorX = Math.floor(x);\n    let floorY = Math.floor(y);\n    let ceilW = Math.ceil(w + x - floorX);\n    let ceilH = Math.ceil(h + y - floorY);\n    excavation = { x: floorX, y: floorY, w: ceilW, h: ceilH };\n  }\n  return { x, y, h, w, excavation };\n}\nvar SUPPORTS_PATH2D = function() {\n  try {\n    new Path2D().addPath(new Path2D());\n  } catch (e) {\n    return false;\n  }\n  return true;\n}();\nfunction QRCodeCanvas(props) {\n  const _a = props, {\n    value,\n    size = DEFAULT_SIZE,\n    level = DEFAULT_LEVEL,\n    bgColor = DEFAULT_BGCOLOR,\n    fgColor = DEFAULT_FGCOLOR,\n    includeMargin = DEFAULT_INCLUDEMARGIN,\n    style,\n    imageSettings\n  } = _a, otherProps = __objRest(_a, [\n    \"value\",\n    \"size\",\n    \"level\",\n    \"bgColor\",\n    \"fgColor\",\n    \"includeMargin\",\n    \"style\",\n    \"imageSettings\"\n  ]);\n  const imgSrc = imageSettings == null ? void 0 : imageSettings.src;\n  const _canvas = React.useRef(null);\n  const _image = React.useRef(null);\n  const [isImgLoaded, setIsImageLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (_canvas.current != null) {\n      const canvas = _canvas.current;\n      const ctx = canvas.getContext(\"2d\");\n      if (!ctx) {\n        return;\n      }\n      let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n      const margin = includeMargin ? MARGIN_SIZE : 0;\n      const numCells = cells.length + margin * 2;\n      const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n      const image = _image.current;\n      const haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;\n      if (haveImageToRender) {\n        if (calculatedImageSettings.excavation != null) {\n          cells = excavateModules(cells, calculatedImageSettings.excavation);\n        }\n      }\n      const pixelRatio = window.devicePixelRatio || 1;\n      canvas.height = canvas.width = size * pixelRatio;\n      const scale = size / numCells * pixelRatio;\n      ctx.scale(scale, scale);\n      ctx.fillStyle = bgColor;\n      ctx.fillRect(0, 0, numCells, numCells);\n      ctx.fillStyle = fgColor;\n      if (SUPPORTS_PATH2D) {\n        ctx.fill(new Path2D(generatePath(cells, margin)));\n      } else {\n        cells.forEach(function(row, rdx) {\n          row.forEach(function(cell, cdx) {\n            if (cell) {\n              ctx.fillRect(cdx + margin, rdx + margin, 1, 1);\n            }\n          });\n        });\n      }\n      if (haveImageToRender) {\n        ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);\n      }\n    }\n  });\n  React.useEffect(() => {\n    setIsImageLoaded(false);\n  }, [imgSrc]);\n  const canvasStyle = __spreadValues({ height: size, width: size }, style);\n  let img = null;\n  if (imgSrc != null) {\n    img = /* @__PURE__ */ React.createElement(\"img\", {\n      src: imgSrc,\n      key: imgSrc,\n      style: { display: \"none\" },\n      onLoad: () => {\n        setIsImageLoaded(true);\n      },\n      ref: _image\n    });\n  }\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\"canvas\", __spreadValues({\n    style: canvasStyle,\n    height: size,\n    width: size,\n    ref: _canvas\n  }, otherProps)), img);\n}\nfunction QRCodeSVG(props) {\n  const _a = props, {\n    value,\n    size = DEFAULT_SIZE,\n    level = DEFAULT_LEVEL,\n    bgColor = DEFAULT_BGCOLOR,\n    fgColor = DEFAULT_FGCOLOR,\n    includeMargin = DEFAULT_INCLUDEMARGIN,\n    imageSettings\n  } = _a, otherProps = __objRest(_a, [\n    \"value\",\n    \"size\",\n    \"level\",\n    \"bgColor\",\n    \"fgColor\",\n    \"includeMargin\",\n    \"imageSettings\"\n  ]);\n  let cells = qrcodegen_default.QrCode.encodeText(value, ERROR_LEVEL_MAP[level]).getModules();\n  const margin = includeMargin ? MARGIN_SIZE : 0;\n  const numCells = cells.length + margin * 2;\n  const calculatedImageSettings = getImageSettings(cells, size, includeMargin, imageSettings);\n  let image = null;\n  if (imageSettings != null && calculatedImageSettings != null) {\n    if (calculatedImageSettings.excavation != null) {\n      cells = excavateModules(cells, calculatedImageSettings.excavation);\n    }\n    image = /* @__PURE__ */ React.createElement(\"image\", {\n      xlinkHref: imageSettings.src,\n      height: calculatedImageSettings.h,\n      width: calculatedImageSettings.w,\n      x: calculatedImageSettings.x + margin,\n      y: calculatedImageSettings.y + margin,\n      preserveAspectRatio: \"none\"\n    });\n  }\n  const fgPath = generatePath(cells, margin);\n  return /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({\n    height: size,\n    width: size,\n    viewBox: `0 0 ${numCells} ${numCells}`\n  }, otherProps), /* @__PURE__ */ React.createElement(\"path\", {\n    fill: bgColor,\n    d: `M0,0 h${numCells}v${numCells}H0z`,\n    shapeRendering: \"crispEdges\"\n  }), /* @__PURE__ */ React.createElement(\"path\", {\n    fill: fgColor,\n    d: fgPath,\n    shapeRendering: \"crispEdges\"\n  }), image);\n}\nvar QRCode = (props) => {\n  const _a = props, { renderAs } = _a, otherProps = __objRest(_a, [\"renderAs\"]);\n  if (renderAs === \"svg\") {\n    return /* @__PURE__ */ React.createElement(QRCodeSVG, __spreadValues({}, otherProps));\n  }\n  return /* @__PURE__ */ React.createElement(QRCodeCanvas, __spreadValues({}, otherProps));\n};\nexport {\n  QRCodeCanvas,\n  QRCodeSVG,\n  QRCode as default\n};\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,MAAM,CAACC,cAAc;AACrC,IAAIC,mBAAmB,GAAGF,MAAM,CAACG,qBAAqB;AACtD,IAAIC,YAAY,GAAGJ,MAAM,CAACK,SAAS,CAACC,cAAc;AAClD,IAAIC,YAAY,GAAGP,MAAM,CAACK,SAAS,CAACG,oBAAoB;AACxD,IAAIC,eAAe,GAAGA,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,KAAKD,GAAG,IAAID,GAAG,GAAGX,SAAS,CAACW,GAAG,EAAEC,GAAG,EAAE;EAAEE,UAAU,EAAE,IAAI;EAAEC,YAAY,EAAE,IAAI;EAAEC,QAAQ,EAAE,IAAI;EAAEH;AAAM,CAAC,CAAC,GAAGF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;AAC/J,IAAII,cAAc,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAK;EAC7B,KAAK,IAAIC,IAAI,IAAID,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC,EAC5B,IAAId,YAAY,CAACgB,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC,IAAIjB,mBAAmB,EACrB,KAAK,IAAIiB,IAAI,IAAIjB,mBAAmB,CAACgB,CAAC,CAAC,EAAE;IACvC,IAAIX,YAAY,CAACa,IAAI,CAACF,CAAC,EAAEC,IAAI,CAAC,EAC5BV,eAAe,CAACQ,CAAC,EAAEE,IAAI,EAAED,CAAC,CAACC,IAAI,CAAC,CAAC;EACrC;EACF,OAAOF,CAAC;AACV,CAAC;AACD,IAAII,SAAS,GAAGA,CAACC,MAAM,EAAEC,OAAO,KAAK;EACnC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIL,IAAI,IAAIG,MAAM,EACrB,IAAIlB,YAAY,CAACgB,IAAI,CAACE,MAAM,EAAEH,IAAI,CAAC,IAAII,OAAO,CAACE,OAAO,CAACN,IAAI,CAAC,GAAG,CAAC,EAC9DK,MAAM,CAACL,IAAI,CAAC,GAAGG,MAAM,CAACH,IAAI,CAAC;EAC/B,IAAIG,MAAM,IAAI,IAAI,IAAIpB,mBAAmB,EACvC,KAAK,IAAIiB,IAAI,IAAIjB,mBAAmB,CAACoB,MAAM,CAAC,EAAE;IAC5C,IAAIC,OAAO,CAACE,OAAO,CAACN,IAAI,CAAC,GAAG,CAAC,IAAIZ,YAAY,CAACa,IAAI,CAACE,MAAM,EAAEH,IAAI,CAAC,EAC9DK,MAAM,CAACL,IAAI,CAAC,GAAGG,MAAM,CAACH,IAAI,CAAC;EAC/B;EACF,OAAOK,MAAM;AACf,CAAC;;AAED;AACA,OAAOE,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS;AACb,CAAEC,UAAU,IAAK;EACf,MAAMC,OAAO,GAAG,MAAM;IACpBC,WAAWA,CAACC,OAAO,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,GAAG,EAAE;MAC7D,IAAI,CAACH,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;MAChD,IAAI,CAACG,OAAO,GAAG,EAAE;MACjB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAIL,OAAO,GAAGF,OAAO,CAACQ,WAAW,IAAIN,OAAO,GAAGF,OAAO,CAACS,WAAW,EAChE,MAAM,IAAIC,UAAU,CAAC,4BAA4B,CAAC;MACpD,IAAIL,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,EACrB,MAAM,IAAIK,UAAU,CAAC,yBAAyB,CAAC;MACjD,IAAI,CAACC,IAAI,GAAGT,OAAO,GAAG,CAAC,GAAG,EAAE;MAC5B,IAAIU,GAAG,GAAG,EAAE;MACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,EAAEE,CAAC,EAAE,EAChCD,GAAG,CAACE,IAAI,CAAC,KAAK,CAAC;MACjB,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,EAAEE,CAAC,EAAE,EAAE;QAClC,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACR,UAAU,CAACO,IAAI,CAACF,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;MACnC;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC3B,MAAMC,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACd,aAAa,CAAC;MAC5D,IAAI,CAACe,aAAa,CAACF,YAAY,CAAC;MAChC,IAAIZ,GAAG,IAAI,CAAC,CAAC,EAAE;QACb,IAAIe,UAAU,GAAG,GAAG;QACpB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,IAAI,CAACQ,SAAS,CAACR,CAAC,CAAC;UACjB,IAAI,CAACS,cAAc,CAACT,CAAC,CAAC;UACtB,MAAMU,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;UACtC,IAAID,OAAO,GAAGH,UAAU,EAAE;YACxBf,GAAG,GAAGQ,CAAC;YACPO,UAAU,GAAGG,OAAO;UACtB;UACA,IAAI,CAACF,SAAS,CAACR,CAAC,CAAC;QACnB;MACF;MACAY,MAAM,CAAC,CAAC,IAAIpB,GAAG,IAAIA,GAAG,IAAI,CAAC,CAAC;MAC5B,IAAI,CAACqB,IAAI,GAAGrB,GAAG;MACf,IAAI,CAACgB,SAAS,CAAChB,GAAG,CAAC;MACnB,IAAI,CAACiB,cAAc,CAACjB,GAAG,CAAC;MACxB,IAAI,CAACE,UAAU,GAAG,EAAE;IACtB;IACA,OAAOoB,UAAUA,CAACC,IAAI,EAAEC,GAAG,EAAE;MAC3B,MAAMC,IAAI,GAAG/B,UAAU,CAACgC,SAAS,CAACC,YAAY,CAACJ,IAAI,CAAC;MACpD,OAAO5B,OAAO,CAACiC,cAAc,CAACH,IAAI,EAAED,GAAG,CAAC;IAC1C;IACA,OAAOK,YAAYA,CAACC,IAAI,EAAEN,GAAG,EAAE;MAC7B,MAAMO,GAAG,GAAGrC,UAAU,CAACgC,SAAS,CAACM,SAAS,CAACF,IAAI,CAAC;MAChD,OAAOnC,OAAO,CAACiC,cAAc,CAAC,CAACG,GAAG,CAAC,EAAEP,GAAG,CAAC;IAC3C;IACA,OAAOI,cAAcA,CAACH,IAAI,EAAED,GAAG,EAAES,UAAU,GAAG,CAAC,EAAEC,UAAU,GAAG,EAAE,EAAEb,IAAI,GAAG,CAAC,CAAC,EAAEc,QAAQ,GAAG,IAAI,EAAE;MAC5F,IAAI,EAAExC,OAAO,CAACQ,WAAW,IAAI8B,UAAU,IAAIA,UAAU,IAAIC,UAAU,IAAIA,UAAU,IAAIvC,OAAO,CAACS,WAAW,CAAC,IAAIiB,IAAI,GAAG,CAAC,CAAC,IAAIA,IAAI,GAAG,CAAC,EAChI,MAAM,IAAIhB,UAAU,CAAC,eAAe,CAAC;MACvC,IAAIR,OAAO;MACX,IAAIuC,YAAY;MAChB,KAAKvC,OAAO,GAAGoC,UAAU,GAAIpC,OAAO,EAAE,EAAE;QACtC,MAAMwC,iBAAiB,GAAG1C,OAAO,CAAC2C,mBAAmB,CAACzC,OAAO,EAAE2B,GAAG,CAAC,GAAG,CAAC;QACvE,MAAMe,QAAQ,GAAGb,SAAS,CAACc,YAAY,CAACf,IAAI,EAAE5B,OAAO,CAAC;QACtD,IAAI0C,QAAQ,IAAIF,iBAAiB,EAAE;UACjCD,YAAY,GAAGG,QAAQ;UACvB;QACF;QACA,IAAI1C,OAAO,IAAIqC,UAAU,EACvB,MAAM,IAAI7B,UAAU,CAAC,eAAe,CAAC;MACzC;MACA,KAAK,MAAMoC,MAAM,IAAI,CAAC9C,OAAO,CAAC+C,GAAG,CAACC,MAAM,EAAEhD,OAAO,CAAC+C,GAAG,CAACE,QAAQ,EAAEjD,OAAO,CAAC+C,GAAG,CAACG,IAAI,CAAC,EAAE;QACjF,IAAIV,QAAQ,IAAIC,YAAY,IAAIzC,OAAO,CAAC2C,mBAAmB,CAACzC,OAAO,EAAE4C,MAAM,CAAC,GAAG,CAAC,EAC9EjB,GAAG,GAAGiB,MAAM;MAChB;MACA,IAAIK,EAAE,GAAG,EAAE;MACX,KAAK,MAAMf,GAAG,IAAIN,IAAI,EAAE;QACtBsB,UAAU,CAAChB,GAAG,CAACiB,IAAI,CAACC,QAAQ,EAAE,CAAC,EAAEH,EAAE,CAAC;QACpCC,UAAU,CAAChB,GAAG,CAACmB,QAAQ,EAAEnB,GAAG,CAACiB,IAAI,CAACG,gBAAgB,CAACtD,OAAO,CAAC,EAAEiD,EAAE,CAAC;QAChE,KAAK,MAAM9D,CAAC,IAAI+C,GAAG,CAACqB,OAAO,CAAC,CAAC,EAC3BN,EAAE,CAACrC,IAAI,CAACzB,CAAC,CAAC;MACd;MACAoC,MAAM,CAAC0B,EAAE,CAACO,MAAM,IAAIjB,YAAY,CAAC;MACjC,MAAMkB,gBAAgB,GAAG3D,OAAO,CAAC2C,mBAAmB,CAACzC,OAAO,EAAE2B,GAAG,CAAC,GAAG,CAAC;MACtEJ,MAAM,CAAC0B,EAAE,CAACO,MAAM,IAAIC,gBAAgB,CAAC;MACrCP,UAAU,CAAC,CAAC,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,gBAAgB,GAAGR,EAAE,CAACO,MAAM,CAAC,EAAEP,EAAE,CAAC;MAC5DC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGD,EAAE,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,EAAEP,EAAE,CAAC;MAC1C1B,MAAM,CAAC0B,EAAE,CAACO,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;MAC1B,KAAK,IAAII,OAAO,GAAG,GAAG,EAAEX,EAAE,CAACO,MAAM,GAAGC,gBAAgB,EAAEG,OAAO,IAAI,GAAG,GAAG,EAAE,EACvEV,UAAU,CAACU,OAAO,EAAE,CAAC,EAAEX,EAAE,CAAC;MAC5B,IAAI/C,aAAa,GAAG,EAAE;MACtB,OAAOA,aAAa,CAACsD,MAAM,GAAG,CAAC,GAAGP,EAAE,CAACO,MAAM,EACzCtD,aAAa,CAACU,IAAI,CAAC,CAAC,CAAC;MACvBqC,EAAE,CAACY,OAAO,CAAC,CAAC1E,CAAC,EAAEwB,CAAC,KAAKT,aAAa,CAACS,CAAC,KAAK,CAAC,CAAC,IAAIxB,CAAC,IAAI,CAAC,IAAIwB,CAAC,GAAG,CAAC,CAAC,CAAC;MAChE,OAAO,IAAIb,OAAO,CAACE,OAAO,EAAE2B,GAAG,EAAEzB,aAAa,EAAEsB,IAAI,CAAC;IACvD;IACAsC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACd,OAAO,CAAC,IAAID,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACtD,IAAI,IAAI,CAAC,IAAIuD,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACvD,IAAI,IAAI,IAAI,CAACL,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC;IACjF;IACAE,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC7D,OAAO;IACrB;IACAU,oBAAoBA,CAAA,EAAG;MACrB,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,EAAEE,CAAC,EAAE,EAAE;QAClC,IAAI,CAACuD,iBAAiB,CAAC,CAAC,EAAEvD,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACxC,IAAI,CAACuD,iBAAiB,CAACvD,CAAC,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAC1C;MACA,IAAI,CAACwD,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,IAAI,CAACA,iBAAiB,CAAC,IAAI,CAAC1D,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;MACxC,IAAI,CAAC0D,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC1D,IAAI,GAAG,CAAC,CAAC;MACxC,MAAM2D,WAAW,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;MACvD,MAAMC,QAAQ,GAAGF,WAAW,CAACZ,MAAM;MACnC,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,QAAQ,EAAE3D,CAAC,EAAE,EAAE;QACjC,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;UACjC,IAAI,EAAE5D,CAAC,IAAI,CAAC,IAAI4D,CAAC,IAAI,CAAC,IAAI5D,CAAC,IAAI,CAAC,IAAI4D,CAAC,IAAID,QAAQ,GAAG,CAAC,IAAI3D,CAAC,IAAI2D,QAAQ,GAAG,CAAC,IAAIC,CAAC,IAAI,CAAC,CAAC,EACnF,IAAI,CAACC,oBAAoB,CAACJ,WAAW,CAACzD,CAAC,CAAC,EAAEyD,WAAW,CAACG,CAAC,CAAC,CAAC;QAC7D;MACF;MACA,IAAI,CAACnD,cAAc,CAAC,CAAC,CAAC;MACtB,IAAI,CAACqD,WAAW,CAAC,CAAC;IACpB;IACArD,cAAcA,CAACI,IAAI,EAAE;MACnB,MAAMS,IAAI,GAAG,IAAI,CAAChC,oBAAoB,CAACyE,UAAU,IAAI,CAAC,GAAGlD,IAAI;MAC7D,IAAImD,GAAG,GAAG1C,IAAI;MACd,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACzBgE,GAAG,GAAGA,GAAG,IAAI,CAAC,GAAG,CAACA,GAAG,KAAK,CAAC,IAAI,IAAI;MACrC,MAAMC,IAAI,GAAG,CAAC3C,IAAI,IAAI,EAAE,GAAG0C,GAAG,IAAI,KAAK;MACvCpD,MAAM,CAACqD,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;MACxB,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EACzB,IAAI,CAACuD,iBAAiB,CAAC,CAAC,EAAEvD,CAAC,EAAEkE,MAAM,CAACD,IAAI,EAAEjE,CAAC,CAAC,CAAC;MAC/C,IAAI,CAACuD,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,IAAI,CAACV,iBAAiB,CAAC,CAAC,EAAE,CAAC,EAAEW,MAAM,CAACD,IAAI,EAAE,CAAC,CAAC,CAAC;MAC7C,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACzB,IAAI,CAACuD,iBAAiB,CAAC,EAAE,GAAGvD,CAAC,EAAE,CAAC,EAAEkE,MAAM,CAACD,IAAI,EAAEjE,CAAC,CAAC,CAAC;MACpD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EACxB,IAAI,CAACuD,iBAAiB,CAAC,IAAI,CAACzD,IAAI,GAAG,CAAC,GAAGE,CAAC,EAAE,CAAC,EAAEkE,MAAM,CAACD,IAAI,EAAEjE,CAAC,CAAC,CAAC;MAC/D,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACzB,IAAI,CAACuD,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACzD,IAAI,GAAG,EAAE,GAAGE,CAAC,EAAEkE,MAAM,CAACD,IAAI,EAAEjE,CAAC,CAAC,CAAC;MAChE,IAAI,CAACuD,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACzD,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC;IAChD;IACAgE,WAAWA,CAAA,EAAG;MACZ,IAAI,IAAI,CAACzE,OAAO,GAAG,CAAC,EAClB;MACF,IAAI2E,GAAG,GAAG,IAAI,CAAC3E,OAAO;MACtB,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EACzBgE,GAAG,GAAGA,GAAG,IAAI,CAAC,GAAG,CAACA,GAAG,KAAK,EAAE,IAAI,IAAI;MACtC,MAAMC,IAAI,GAAG,IAAI,CAAC5E,OAAO,IAAI,EAAE,GAAG2E,GAAG;MACrCpD,MAAM,CAACqD,IAAI,KAAK,EAAE,IAAI,CAAC,CAAC;MACxB,KAAK,IAAIjE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,MAAMmE,KAAK,GAAGD,MAAM,CAACD,IAAI,EAAEjE,CAAC,CAAC;QAC7B,MAAMzB,CAAC,GAAG,IAAI,CAACuB,IAAI,GAAG,EAAE,GAAGE,CAAC,GAAG,CAAC;QAChC,MAAMxB,CAAC,GAAGuE,IAAI,CAACqB,KAAK,CAACpE,CAAC,GAAG,CAAC,CAAC;QAC3B,IAAI,CAACuD,iBAAiB,CAAChF,CAAC,EAAEC,CAAC,EAAE2F,KAAK,CAAC;QACnC,IAAI,CAACZ,iBAAiB,CAAC/E,CAAC,EAAED,CAAC,EAAE4F,KAAK,CAAC;MACrC;IACF;IACAX,iBAAiBA,CAACJ,CAAC,EAAEC,CAAC,EAAE;MACtB,KAAK,IAAIgB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;UAC/B,MAAMC,IAAI,GAAGxB,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAAC0B,GAAG,CAACH,EAAE,CAAC,EAAEvB,IAAI,CAAC0B,GAAG,CAACJ,EAAE,CAAC,CAAC;UACjD,MAAMK,EAAE,GAAGtB,CAAC,GAAGkB,EAAE;UACjB,MAAMK,EAAE,GAAGtB,CAAC,GAAGgB,EAAE;UACjB,IAAI,CAAC,IAAIK,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC5E,IAAI,IAAI,CAAC,IAAI6E,EAAE,IAAIA,EAAE,GAAG,IAAI,CAAC7E,IAAI,EACxD,IAAI,CAACyD,iBAAiB,CAACmB,EAAE,EAAEC,EAAE,EAAEJ,IAAI,IAAI,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC;QAC1D;MACF;IACF;IACAV,oBAAoBA,CAACT,CAAC,EAAEC,CAAC,EAAE;MACzB,KAAK,IAAIgB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAAE;QAC/B,KAAK,IAAIC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,IAAI,CAAC,EAAEA,EAAE,EAAE,EAC7B,IAAI,CAACf,iBAAiB,CAACH,CAAC,GAAGkB,EAAE,EAAEjB,CAAC,GAAGgB,EAAE,EAAEtB,IAAI,CAACyB,GAAG,CAACzB,IAAI,CAAC0B,GAAG,CAACH,EAAE,CAAC,EAAEvB,IAAI,CAAC0B,GAAG,CAACJ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;MACrF;IACF;IACAd,iBAAiBA,CAACH,CAAC,EAAEC,CAAC,EAAEuB,MAAM,EAAE;MAC9B,IAAI,CAACnF,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC,GAAGwB,MAAM;MAC3B,IAAI,CAAClF,UAAU,CAAC2D,CAAC,CAAC,CAACD,CAAC,CAAC,GAAG,IAAI;IAC9B;IACA/C,mBAAmBA,CAACiB,IAAI,EAAE;MACxB,MAAMuD,GAAG,GAAG,IAAI,CAACxF,OAAO;MACxB,MAAM2B,GAAG,GAAG,IAAI,CAAC1B,oBAAoB;MACrC,IAAIgC,IAAI,CAACuB,MAAM,IAAI1D,OAAO,CAAC2C,mBAAmB,CAAC+C,GAAG,EAAE7D,GAAG,CAAC,EACtD,MAAM,IAAInB,UAAU,CAAC,kBAAkB,CAAC;MAC1C,MAAMiF,SAAS,GAAG3F,OAAO,CAAC4F,2BAA2B,CAAC/D,GAAG,CAACgE,OAAO,CAAC,CAACH,GAAG,CAAC;MACvE,MAAMI,WAAW,GAAG9F,OAAO,CAAC+F,uBAAuB,CAAClE,GAAG,CAACgE,OAAO,CAAC,CAACH,GAAG,CAAC;MACrE,MAAMM,YAAY,GAAGpC,IAAI,CAACqB,KAAK,CAACjF,OAAO,CAACiG,oBAAoB,CAACP,GAAG,CAAC,GAAG,CAAC,CAAC;MACtE,MAAMQ,cAAc,GAAGP,SAAS,GAAGK,YAAY,GAAGL,SAAS;MAC3D,MAAMQ,aAAa,GAAGvC,IAAI,CAACqB,KAAK,CAACe,YAAY,GAAGL,SAAS,CAAC;MAC1D,IAAIS,MAAM,GAAG,EAAE;MACf,MAAMC,KAAK,GAAGrG,OAAO,CAACsG,yBAAyB,CAACR,WAAW,CAAC;MAC5D,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAE0F,CAAC,GAAG,CAAC,EAAE1F,CAAC,GAAG8E,SAAS,EAAE9E,CAAC,EAAE,EAAE;QACzC,IAAI2F,GAAG,GAAGrE,IAAI,CAACpB,KAAK,CAACwF,CAAC,EAAEA,CAAC,GAAGJ,aAAa,GAAGL,WAAW,IAAIjF,CAAC,GAAGqF,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACvFK,CAAC,IAAIC,GAAG,CAAC9C,MAAM;QACf,MAAM+C,GAAG,GAAGzG,OAAO,CAAC0G,2BAA2B,CAACF,GAAG,EAAEH,KAAK,CAAC;QAC3D,IAAIxF,CAAC,GAAGqF,cAAc,EACpBM,GAAG,CAAC1F,IAAI,CAAC,CAAC,CAAC;QACbsF,MAAM,CAACtF,IAAI,CAAC0F,GAAG,CAACG,MAAM,CAACF,GAAG,CAAC,CAAC;MAC9B;MACA,IAAIG,MAAM,GAAG,EAAE;MACf,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuF,MAAM,CAAC,CAAC,CAAC,CAAC1C,MAAM,EAAE7C,CAAC,EAAE,EAAE;QACzCuF,MAAM,CAACrC,OAAO,CAAC,CAAC8C,KAAK,EAAEpC,CAAC,KAAK;UAC3B,IAAI5D,CAAC,IAAIsF,aAAa,GAAGL,WAAW,IAAIrB,CAAC,IAAIyB,cAAc,EACzDU,MAAM,CAAC9F,IAAI,CAAC+F,KAAK,CAAChG,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC;MACJ;MACAY,MAAM,CAACmF,MAAM,CAAClD,MAAM,IAAIsC,YAAY,CAAC;MACrC,OAAOY,MAAM;IACf;IACAzF,aAAaA,CAACgB,IAAI,EAAE;MAClB,IAAIA,IAAI,CAACuB,MAAM,IAAIE,IAAI,CAACqB,KAAK,CAACjF,OAAO,CAACiG,oBAAoB,CAAC,IAAI,CAAC/F,OAAO,CAAC,GAAG,CAAC,CAAC,EAC3E,MAAM,IAAIQ,UAAU,CAAC,kBAAkB,CAAC;MAC1C,IAAIG,CAAC,GAAG,CAAC;MACT,KAAK,IAAIiG,KAAK,GAAG,IAAI,CAACnG,IAAI,GAAG,CAAC,EAAEmG,KAAK,IAAI,CAAC,EAAEA,KAAK,IAAI,CAAC,EAAE;QACtD,IAAIA,KAAK,IAAI,CAAC,EACZA,KAAK,GAAG,CAAC;QACX,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,IAAI,CAACpG,IAAI,EAAEoG,IAAI,EAAE,EAAE;UAC3C,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC1B,MAAMR,CAAC,GAAG6C,KAAK,GAAGrC,CAAC;YACnB,MAAMuC,MAAM,GAAG,CAACF,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;YACnC,MAAM5C,CAAC,GAAG8C,MAAM,GAAG,IAAI,CAACrG,IAAI,GAAG,CAAC,GAAGoG,IAAI,GAAGA,IAAI;YAC9C,IAAI,CAAC,IAAI,CAACxG,UAAU,CAAC2D,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIpD,CAAC,GAAGsB,IAAI,CAACuB,MAAM,GAAG,CAAC,EAAE;cACjD,IAAI,CAACpD,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC,GAAGc,MAAM,CAAC5C,IAAI,CAACtB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,CAAC;cACvDA,CAAC,EAAE;YACL;UACF;QACF;MACF;MACAY,MAAM,CAACZ,CAAC,IAAIsB,IAAI,CAACuB,MAAM,GAAG,CAAC,CAAC;IAC9B;IACArC,SAASA,CAACK,IAAI,EAAE;MACd,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,CAAC,EACtB,MAAM,IAAIhB,UAAU,CAAC,yBAAyB,CAAC;MACjD,KAAK,IAAIwD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvD,IAAI,EAAEuD,CAAC,EAAE,EAAE;QAClC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtD,IAAI,EAAEsD,CAAC,EAAE,EAAE;UAClC,IAAIgD,MAAM;UACV,QAAQvF,IAAI;YACV,KAAK,CAAC;cACJuF,MAAM,GAAG,CAAChD,CAAC,GAAGC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAG/C,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAGhD,CAAC,GAAG,CAAC,IAAI,CAAC;cACnB;YACF,KAAK,CAAC;cACJgD,MAAM,GAAG,CAAChD,CAAC,GAAGC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzB;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAG,CAACrD,IAAI,CAACqB,KAAK,CAAChB,CAAC,GAAG,CAAC,CAAC,GAAGL,IAAI,CAACqB,KAAK,CAACf,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;cACzD;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAGhD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC;cACnC;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAG,CAAChD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cACzC;YACF,KAAK,CAAC;cACJ+C,MAAM,GAAG,CAAC,CAAChD,CAAC,GAAGC,CAAC,IAAI,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;cAC3C;YACF;cACE,MAAM,IAAIgD,KAAK,CAAC,aAAa,CAAC;UAClC;UACA,IAAI,CAAC,IAAI,CAAC3G,UAAU,CAAC2D,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIgD,MAAM,EAClC,IAAI,CAAC3G,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC3D,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC;QAC5C;MACF;IACF;IACAzC,eAAeA,CAAA,EAAG;MAChB,IAAIoF,MAAM,GAAG,CAAC;MACd,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvD,IAAI,EAAEuD,CAAC,EAAE,EAAE;QAClC,IAAIiD,QAAQ,GAAG,KAAK;QACpB,IAAIC,IAAI,GAAG,CAAC;QACZ,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtD,IAAI,EAAEsD,CAAC,EAAE,EAAE;UAClC,IAAI,IAAI,CAAC3D,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIkD,QAAQ,EAAE;YAClCC,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EACXR,MAAM,IAAI5G,OAAO,CAACsH,UAAU,CAAC,KAC1B,IAAIF,IAAI,GAAG,CAAC,EACfR,MAAM,EAAE;UACZ,CAAC,MAAM;YACL,IAAI,CAACW,uBAAuB,CAACH,IAAI,EAAEC,UAAU,CAAC;YAC9C,IAAI,CAACF,QAAQ,EACXP,MAAM,IAAI,IAAI,CAACY,0BAA0B,CAACH,UAAU,CAAC,GAAGrH,OAAO,CAACyH,UAAU;YAC5EN,QAAQ,GAAG,IAAI,CAAC7G,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC;YAC7BmD,IAAI,GAAG,CAAC;UACV;QACF;QACAR,MAAM,IAAI,IAAI,CAACc,8BAA8B,CAACP,QAAQ,EAAEC,IAAI,EAAEC,UAAU,CAAC,GAAGrH,OAAO,CAACyH,UAAU;MAChG;MACA,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtD,IAAI,EAAEsD,CAAC,EAAE,EAAE;QAClC,IAAIkD,QAAQ,GAAG,KAAK;QACpB,IAAIQ,IAAI,GAAG,CAAC;QACZ,IAAIN,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACtC,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvD,IAAI,EAAEuD,CAAC,EAAE,EAAE;UAClC,IAAI,IAAI,CAAC5D,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIkD,QAAQ,EAAE;YAClCQ,IAAI,EAAE;YACN,IAAIA,IAAI,IAAI,CAAC,EACXf,MAAM,IAAI5G,OAAO,CAACsH,UAAU,CAAC,KAC1B,IAAIK,IAAI,GAAG,CAAC,EACff,MAAM,EAAE;UACZ,CAAC,MAAM;YACL,IAAI,CAACW,uBAAuB,CAACI,IAAI,EAAEN,UAAU,CAAC;YAC9C,IAAI,CAACF,QAAQ,EACXP,MAAM,IAAI,IAAI,CAACY,0BAA0B,CAACH,UAAU,CAAC,GAAGrH,OAAO,CAACyH,UAAU;YAC5EN,QAAQ,GAAG,IAAI,CAAC7G,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC;YAC7B0D,IAAI,GAAG,CAAC;UACV;QACF;QACAf,MAAM,IAAI,IAAI,CAACc,8BAA8B,CAACP,QAAQ,EAAEQ,IAAI,EAAEN,UAAU,CAAC,GAAGrH,OAAO,CAACyH,UAAU;MAChG;MACA,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvD,IAAI,GAAG,CAAC,EAAEuD,CAAC,EAAE,EAAE;QACtC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtD,IAAI,GAAG,CAAC,EAAEsD,CAAC,EAAE,EAAE;UACtC,MAAMe,KAAK,GAAG,IAAI,CAAC1E,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,CAAC;UAChC,IAAIe,KAAK,IAAI,IAAI,CAAC1E,OAAO,CAAC4D,CAAC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,IAAIe,KAAK,IAAI,IAAI,CAAC1E,OAAO,CAAC4D,CAAC,GAAG,CAAC,CAAC,CAACD,CAAC,CAAC,IAAIe,KAAK,IAAI,IAAI,CAAC1E,OAAO,CAAC4D,CAAC,GAAG,CAAC,CAAC,CAACD,CAAC,GAAG,CAAC,CAAC,EAC3G2C,MAAM,IAAI5G,OAAO,CAAC4H,UAAU;QAChC;MACF;MACA,IAAIC,IAAI,GAAG,CAAC;MACZ,KAAK,MAAMjH,GAAG,IAAI,IAAI,CAACN,OAAO,EAC5BuH,IAAI,GAAGjH,GAAG,CAACkH,MAAM,CAAC,CAACC,GAAG,EAAE/C,KAAK,KAAK+C,GAAG,IAAI/C,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE6C,IAAI,CAAC;MAChE,MAAMG,KAAK,GAAG,IAAI,CAACrH,IAAI,GAAG,IAAI,CAACA,IAAI;MACnC,MAAM4F,CAAC,GAAG3C,IAAI,CAACqE,IAAI,CAACrE,IAAI,CAAC0B,GAAG,CAACuC,IAAI,GAAG,EAAE,GAAGG,KAAK,GAAG,EAAE,CAAC,GAAGA,KAAK,CAAC,GAAG,CAAC;MACjEvG,MAAM,CAAC,CAAC,IAAI8E,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC;MACxBK,MAAM,IAAIL,CAAC,GAAGvG,OAAO,CAACkI,UAAU;MAChCzG,MAAM,CAAC,CAAC,IAAImF,MAAM,IAAIA,MAAM,IAAI,OAAO,CAAC;MACxC,OAAOA,MAAM;IACf;IACArC,4BAA4BA,CAAA,EAAG;MAC7B,IAAI,IAAI,CAACrE,OAAO,IAAI,CAAC,EACnB,OAAO,EAAE,CAAC,KACP;QACH,MAAMsE,QAAQ,GAAGZ,IAAI,CAACqB,KAAK,CAAC,IAAI,CAAC/E,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC;QACjD,MAAMiI,IAAI,GAAG,IAAI,CAACjI,OAAO,IAAI,EAAE,GAAG,EAAE,GAAG0D,IAAI,CAACqE,IAAI,CAAC,CAAC,IAAI,CAAC/H,OAAO,GAAG,CAAC,GAAG,CAAC,KAAKsE,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACjG,IAAIoC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChB,KAAK,IAAIwB,GAAG,GAAG,IAAI,CAACzH,IAAI,GAAG,CAAC,EAAEiG,MAAM,CAAClD,MAAM,GAAGc,QAAQ,EAAE4D,GAAG,IAAID,IAAI,EACjEvB,MAAM,CAACyB,MAAM,CAAC,CAAC,EAAE,CAAC,EAAED,GAAG,CAAC;QAC1B,OAAOxB,MAAM;MACf;IACF;IACA,OAAOX,oBAAoBA,CAACP,GAAG,EAAE;MAC/B,IAAIA,GAAG,GAAG1F,OAAO,CAACQ,WAAW,IAAIkF,GAAG,GAAG1F,OAAO,CAACS,WAAW,EACxD,MAAM,IAAIC,UAAU,CAAC,6BAA6B,CAAC;MACrD,IAAIkG,MAAM,GAAG,CAAC,EAAE,GAAGlB,GAAG,GAAG,GAAG,IAAIA,GAAG,GAAG,EAAE;MACxC,IAAIA,GAAG,IAAI,CAAC,EAAE;QACZ,MAAMlB,QAAQ,GAAGZ,IAAI,CAACqB,KAAK,CAACS,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;QACxCkB,MAAM,IAAI,CAAC,EAAE,GAAGpC,QAAQ,GAAG,EAAE,IAAIA,QAAQ,GAAG,EAAE;QAC9C,IAAIkB,GAAG,IAAI,CAAC,EACVkB,MAAM,IAAI,EAAE;MAChB;MACAnF,MAAM,CAAC,GAAG,IAAImF,MAAM,IAAIA,MAAM,IAAI,KAAK,CAAC;MACxC,OAAOA,MAAM;IACf;IACA,OAAOjE,mBAAmBA,CAAC+C,GAAG,EAAE7D,GAAG,EAAE;MACnC,OAAO+B,IAAI,CAACqB,KAAK,CAACjF,OAAO,CAACiG,oBAAoB,CAACP,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG1F,OAAO,CAAC+F,uBAAuB,CAAClE,GAAG,CAACgE,OAAO,CAAC,CAACH,GAAG,CAAC,GAAG1F,OAAO,CAAC4F,2BAA2B,CAAC/D,GAAG,CAACgE,OAAO,CAAC,CAACH,GAAG,CAAC;IACtK;IACA,OAAOY,yBAAyBA,CAACgC,MAAM,EAAE;MACvC,IAAIA,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,GAAG,EAC5B,MAAM,IAAI5H,UAAU,CAAC,qBAAqB,CAAC;MAC7C,IAAIkG,MAAM,GAAG,EAAE;MACf,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,MAAM,GAAG,CAAC,EAAEzH,CAAC,EAAE,EACjC+F,MAAM,CAAC9F,IAAI,CAAC,CAAC,CAAC;MAChB8F,MAAM,CAAC9F,IAAI,CAAC,CAAC,CAAC;MACd,IAAIyH,IAAI,GAAG,CAAC;MACZ,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyH,MAAM,EAAEzH,CAAC,EAAE,EAAE;QAC/B,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmC,MAAM,CAAClD,MAAM,EAAEe,CAAC,EAAE,EAAE;UACtCmC,MAAM,CAACnC,CAAC,CAAC,GAAGzE,OAAO,CAACwI,mBAAmB,CAAC5B,MAAM,CAACnC,CAAC,CAAC,EAAE8D,IAAI,CAAC;UACxD,IAAI9D,CAAC,GAAG,CAAC,GAAGmC,MAAM,CAAClD,MAAM,EACvBkD,MAAM,CAACnC,CAAC,CAAC,IAAImC,MAAM,CAACnC,CAAC,GAAG,CAAC,CAAC;QAC9B;QACA8D,IAAI,GAAGvI,OAAO,CAACwI,mBAAmB,CAACD,IAAI,EAAE,CAAC,CAAC;MAC7C;MACA,OAAO3B,MAAM;IACf;IACA,OAAOF,2BAA2BA,CAACvE,IAAI,EAAEsG,OAAO,EAAE;MAChD,IAAI7B,MAAM,GAAG6B,OAAO,CAACC,GAAG,CAAEC,CAAC,IAAK,CAAC,CAAC;MAClC,KAAK,MAAMtJ,CAAC,IAAI8C,IAAI,EAAE;QACpB,MAAMyG,MAAM,GAAGvJ,CAAC,GAAGuH,MAAM,CAACiC,KAAK,CAAC,CAAC;QACjCjC,MAAM,CAAC9F,IAAI,CAAC,CAAC,CAAC;QACd2H,OAAO,CAAC1E,OAAO,CAAC,CAAC+E,IAAI,EAAEjI,CAAC,KAAK+F,MAAM,CAAC/F,CAAC,CAAC,IAAIb,OAAO,CAACwI,mBAAmB,CAACM,IAAI,EAAEF,MAAM,CAAC,CAAC;MACtF;MACA,OAAOhC,MAAM;IACf;IACA,OAAO4B,mBAAmBA,CAACvE,CAAC,EAAEC,CAAC,EAAE;MAC/B,IAAID,CAAC,KAAK,CAAC,IAAI,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAI,CAAC,EAC9B,MAAM,IAAIxD,UAAU,CAAC,mBAAmB,CAAC;MAC3C,IAAIqI,CAAC,GAAG,CAAC;MACT,KAAK,IAAIlI,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3BkI,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,KAAK,CAAC,IAAI,GAAG;QAC5BA,CAAC,IAAI,CAAC7E,CAAC,KAAKrD,CAAC,GAAG,CAAC,IAAIoD,CAAC;MACxB;MACAxC,MAAM,CAACsH,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;MACpB,OAAOA,CAAC;IACV;IACAvB,0BAA0BA,CAACH,UAAU,EAAE;MACrC,MAAM2B,CAAC,GAAG3B,UAAU,CAAC,CAAC,CAAC;MACvB5F,MAAM,CAACuH,CAAC,IAAI,IAAI,CAACrI,IAAI,GAAG,CAAC,CAAC;MAC1B,MAAMsI,IAAI,GAAGD,CAAC,GAAG,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,GAAG,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC;MAC9G,OAAO,CAACC,IAAI,IAAI5B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,GAAG,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,GAAG,CAAC,GAAG,CAAC,KAAKC,IAAI,IAAI5B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,GAAG,CAAC,IAAI3B,UAAU,CAAC,CAAC,CAAC,IAAI2B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxI;IACAtB,8BAA8BA,CAACwB,eAAe,EAAEC,gBAAgB,EAAE9B,UAAU,EAAE;MAC5E,IAAI6B,eAAe,EAAE;QACnB,IAAI,CAAC3B,uBAAuB,CAAC4B,gBAAgB,EAAE9B,UAAU,CAAC;QAC1D8B,gBAAgB,GAAG,CAAC;MACtB;MACAA,gBAAgB,IAAI,IAAI,CAACxI,IAAI;MAC7B,IAAI,CAAC4G,uBAAuB,CAAC4B,gBAAgB,EAAE9B,UAAU,CAAC;MAC1D,OAAO,IAAI,CAACG,0BAA0B,CAACH,UAAU,CAAC;IACpD;IACAE,uBAAuBA,CAAC4B,gBAAgB,EAAE9B,UAAU,EAAE;MACpD,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EACpB8B,gBAAgB,IAAI,IAAI,CAACxI,IAAI;MAC/B0G,UAAU,CAAC+B,GAAG,CAAC,CAAC;MAChB/B,UAAU,CAACgC,OAAO,CAACF,gBAAgB,CAAC;IACtC;EACF,CAAC;EACD,IAAIG,MAAM,GAAGtJ,OAAO;EACpBsJ,MAAM,CAAC9I,WAAW,GAAG,CAAC;EACtB8I,MAAM,CAAC7I,WAAW,GAAG,EAAE;EACvB6I,MAAM,CAAChC,UAAU,GAAG,CAAC;EACrBgC,MAAM,CAAC1B,UAAU,GAAG,CAAC;EACrB0B,MAAM,CAAC7B,UAAU,GAAG,EAAE;EACtB6B,MAAM,CAACpB,UAAU,GAAG,EAAE;EACtBoB,MAAM,CAACvD,uBAAuB,GAAG,CAC/B,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACnK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACpK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACpK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CACrK;EACDuD,MAAM,CAAC1D,2BAA2B,GAAG,CACnC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAC7I,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACtJ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACzJ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAC3J;EACD7F,UAAU,CAACuJ,MAAM,GAAGA,MAAM;EAC1B,SAASlG,UAAUA,CAACmG,GAAG,EAAEC,GAAG,EAAErG,EAAE,EAAE;IAChC,IAAIqG,GAAG,GAAG,CAAC,IAAIA,GAAG,GAAG,EAAE,IAAID,GAAG,KAAKC,GAAG,IAAI,CAAC,EACzC,MAAM,IAAI9I,UAAU,CAAC,oBAAoB,CAAC;IAC5C,KAAK,IAAIG,CAAC,GAAG2I,GAAG,GAAG,CAAC,EAAE3I,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAC/BsC,EAAE,CAACrC,IAAI,CAACyI,GAAG,KAAK1I,CAAC,GAAG,CAAC,CAAC;EAC1B;EACA,SAASkE,MAAMA,CAACd,CAAC,EAAEpD,CAAC,EAAE;IACpB,OAAO,CAACoD,CAAC,KAAKpD,CAAC,GAAG,CAAC,KAAK,CAAC;EAC3B;EACA,SAASY,MAAMA,CAACgI,IAAI,EAAE;IACpB,IAAI,CAACA,IAAI,EACP,MAAM,IAAIvC,KAAK,CAAC,iBAAiB,CAAC;EACtC;EACA,MAAMwC,UAAU,GAAG,MAAM;IACvBzJ,WAAWA,CAACoD,IAAI,EAAEE,QAAQ,EAAEoG,OAAO,EAAE;MACnC,IAAI,CAACtG,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACE,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACoG,OAAO,GAAGA,OAAO;MACtB,IAAIpG,QAAQ,GAAG,CAAC,EACd,MAAM,IAAI7C,UAAU,CAAC,kBAAkB,CAAC;MAC1C,IAAI,CAACiJ,OAAO,GAAGA,OAAO,CAAC5I,KAAK,CAAC,CAAC;IAChC;IACA,OAAOsB,SAASA,CAACF,IAAI,EAAE;MACrB,IAAIgB,EAAE,GAAG,EAAE;MACX,KAAK,MAAM9D,CAAC,IAAI8C,IAAI,EAClBiB,UAAU,CAAC/D,CAAC,EAAE,CAAC,EAAE8D,EAAE,CAAC;MACtB,OAAO,IAAIuG,UAAU,CAACA,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE1H,IAAI,CAACuB,MAAM,EAAEP,EAAE,CAAC;IAC9D;IACA,OAAO2G,WAAWA,CAACC,MAAM,EAAE;MACzB,IAAI,CAACL,UAAU,CAACM,SAAS,CAACD,MAAM,CAAC,EAC/B,MAAM,IAAIrJ,UAAU,CAAC,wCAAwC,CAAC;MAChE,IAAIyC,EAAE,GAAG,EAAE;MACX,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkJ,MAAM,CAACrG,MAAM,GAAI;QACnC,MAAMsF,CAAC,GAAGpF,IAAI,CAACC,GAAG,CAACkG,MAAM,CAACrG,MAAM,GAAG7C,CAAC,EAAE,CAAC,CAAC;QACxCuC,UAAU,CAAC6G,QAAQ,CAACF,MAAM,CAACG,MAAM,CAACrJ,CAAC,EAAEmI,CAAC,CAAC,EAAE,EAAE,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE7F,EAAE,CAAC;QAC5DtC,CAAC,IAAImI,CAAC;MACR;MACA,OAAO,IAAIU,UAAU,CAACA,UAAU,CAACE,IAAI,CAACO,OAAO,EAAEJ,MAAM,CAACrG,MAAM,EAAEP,EAAE,CAAC;IACnE;IACA,OAAOiH,gBAAgBA,CAACxI,IAAI,EAAE;MAC5B,IAAI,CAAC8H,UAAU,CAACW,cAAc,CAACzI,IAAI,CAAC,EAClC,MAAM,IAAIlB,UAAU,CAAC,6DAA6D,CAAC;MACrF,IAAIyC,EAAE,GAAG,EAAE;MACX,IAAItC,CAAC;MACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIe,IAAI,CAAC8B,MAAM,EAAE7C,CAAC,IAAI,CAAC,EAAE;QACxC,IAAIyJ,IAAI,GAAGZ,UAAU,CAACa,oBAAoB,CAAC3K,OAAO,CAACgC,IAAI,CAAC4I,MAAM,CAAC3J,CAAC,CAAC,CAAC,GAAG,EAAE;QACvEyJ,IAAI,IAAIZ,UAAU,CAACa,oBAAoB,CAAC3K,OAAO,CAACgC,IAAI,CAAC4I,MAAM,CAAC3J,CAAC,GAAG,CAAC,CAAC,CAAC;QACnEuC,UAAU,CAACkH,IAAI,EAAE,EAAE,EAAEnH,EAAE,CAAC;MAC1B;MACA,IAAItC,CAAC,GAAGe,IAAI,CAAC8B,MAAM,EACjBN,UAAU,CAACsG,UAAU,CAACa,oBAAoB,CAAC3K,OAAO,CAACgC,IAAI,CAAC4I,MAAM,CAAC3J,CAAC,CAAC,CAAC,EAAE,CAAC,EAAEsC,EAAE,CAAC;MAC5E,OAAO,IAAIuG,UAAU,CAACA,UAAU,CAACE,IAAI,CAACa,YAAY,EAAE7I,IAAI,CAAC8B,MAAM,EAAEP,EAAE,CAAC;IACtE;IACA,OAAOnB,YAAYA,CAACJ,IAAI,EAAE;MACxB,IAAIA,IAAI,IAAI,EAAE,EACZ,OAAO,EAAE,CAAC,KACP,IAAI8H,UAAU,CAACM,SAAS,CAACpI,IAAI,CAAC,EACjC,OAAO,CAAC8H,UAAU,CAACI,WAAW,CAAClI,IAAI,CAAC,CAAC,CAAC,KACnC,IAAI8H,UAAU,CAACW,cAAc,CAACzI,IAAI,CAAC,EACtC,OAAO,CAAC8H,UAAU,CAACU,gBAAgB,CAACxI,IAAI,CAAC,CAAC,CAAC,KAE3C,OAAO,CAAC8H,UAAU,CAACrH,SAAS,CAACqH,UAAU,CAACgB,eAAe,CAAC9I,IAAI,CAAC,CAAC,CAAC;IACnE;IACA,OAAO+I,OAAOA,CAACC,SAAS,EAAE;MACxB,IAAIzH,EAAE,GAAG,EAAE;MACX,IAAIyH,SAAS,GAAG,CAAC,EACf,MAAM,IAAIlK,UAAU,CAAC,mCAAmC,CAAC,CAAC,KACvD,IAAIkK,SAAS,GAAG,CAAC,IAAI,CAAC,EACzBxH,UAAU,CAACwH,SAAS,EAAE,CAAC,EAAEzH,EAAE,CAAC,CAAC,KAC1B,IAAIyH,SAAS,GAAG,CAAC,IAAI,EAAE,EAAE;QAC5BxH,UAAU,CAAC,CAAC,EAAE,CAAC,EAAED,EAAE,CAAC;QACpBC,UAAU,CAACwH,SAAS,EAAE,EAAE,EAAEzH,EAAE,CAAC;MAC/B,CAAC,MAAM,IAAIyH,SAAS,GAAG,GAAG,EAAE;QAC1BxH,UAAU,CAAC,CAAC,EAAE,CAAC,EAAED,EAAE,CAAC;QACpBC,UAAU,CAACwH,SAAS,EAAE,EAAE,EAAEzH,EAAE,CAAC;MAC/B,CAAC,MACC,MAAM,IAAIzC,UAAU,CAAC,mCAAmC,CAAC;MAC3D,OAAO,IAAIgJ,UAAU,CAACA,UAAU,CAACE,IAAI,CAACiB,GAAG,EAAE,CAAC,EAAE1H,EAAE,CAAC;IACnD;IACA,OAAO6G,SAASA,CAACpI,IAAI,EAAE;MACrB,OAAO8H,UAAU,CAACoB,aAAa,CAACC,IAAI,CAACnJ,IAAI,CAAC;IAC5C;IACA,OAAOyI,cAAcA,CAACzI,IAAI,EAAE;MAC1B,OAAO8H,UAAU,CAACsB,kBAAkB,CAACD,IAAI,CAACnJ,IAAI,CAAC;IACjD;IACA6B,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACkG,OAAO,CAAC5I,KAAK,CAAC,CAAC;IAC7B;IACA,OAAO8B,YAAYA,CAACf,IAAI,EAAE5B,OAAO,EAAE;MACjC,IAAI0G,MAAM,GAAG,CAAC;MACd,KAAK,MAAMxE,GAAG,IAAIN,IAAI,EAAE;QACtB,MAAMmJ,MAAM,GAAG7I,GAAG,CAACiB,IAAI,CAACG,gBAAgB,CAACtD,OAAO,CAAC;QACjD,IAAIkC,GAAG,CAACmB,QAAQ,IAAI,CAAC,IAAI0H,MAAM,EAC7B,OAAOC,QAAQ;QACjBtE,MAAM,IAAI,CAAC,GAAGqE,MAAM,GAAG7I,GAAG,CAACuH,OAAO,CAACjG,MAAM;MAC3C;MACA,OAAOkD,MAAM;IACf;IACA,OAAO8D,eAAeA,CAACS,GAAG,EAAE;MAC1BA,GAAG,GAAGC,SAAS,CAACD,GAAG,CAAC;MACpB,IAAIvE,MAAM,GAAG,EAAE;MACf,KAAK,IAAI/F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsK,GAAG,CAACzH,MAAM,EAAE7C,CAAC,EAAE,EAAE;QACnC,IAAIsK,GAAG,CAACX,MAAM,CAAC3J,CAAC,CAAC,IAAI,GAAG,EACtB+F,MAAM,CAAC9F,IAAI,CAACqK,GAAG,CAACE,UAAU,CAACxK,CAAC,CAAC,CAAC,CAAC,KAC5B;UACH+F,MAAM,CAAC9F,IAAI,CAACmJ,QAAQ,CAACkB,GAAG,CAACjB,MAAM,CAACrJ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;UAC/CA,CAAC,IAAI,CAAC;QACR;MACF;MACA,OAAO+F,MAAM;IACf;EACF,CAAC;EACD,IAAI7E,SAAS,GAAG2H,UAAU;EAC1B3H,SAAS,CAAC+I,aAAa,GAAG,UAAU;EACpC/I,SAAS,CAACiJ,kBAAkB,GAAG,uBAAuB;EACtDjJ,SAAS,CAACwI,oBAAoB,GAAG,+CAA+C;EAChFxK,UAAU,CAACgC,SAAS,GAAGA,SAAS;AAClC,CAAC,EAAEjC,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,CAAEC,UAAU,IAAK;EACf,IAAIuJ,MAAM;EACV,CAAEgC,OAAO,IAAK;IACZ,MAAMC,IAAI,GAAG,MAAM;MACjBtL,WAAWA,CAAC4F,OAAO,EAAEjB,UAAU,EAAE;QAC/B,IAAI,CAACiB,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACjB,UAAU,GAAGA,UAAU;MAC9B;IACF,CAAC;IACD,IAAI7B,GAAG,GAAGwI,IAAI;IACdxI,GAAG,CAACyI,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACxBxI,GAAG,CAACC,MAAM,GAAG,IAAIuI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3BxI,GAAG,CAACE,QAAQ,GAAG,IAAIsI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7BxI,GAAG,CAACG,IAAI,GAAG,IAAIqI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IACzBD,OAAO,CAACvI,GAAG,GAAGA,GAAG;EACnB,CAAC,EAAEuG,MAAM,GAAGvJ,UAAU,CAACuJ,MAAM,KAAKvJ,UAAU,CAACuJ,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,EAAExJ,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,CAAEC,UAAU,IAAK;EACf,IAAIgC,SAAS;EACb,CAAE0J,UAAU,IAAK;IACf,MAAMC,KAAK,GAAG,MAAM;MAClBzL,WAAWA,CAACqD,QAAQ,EAAEqI,gBAAgB,EAAE;QACtC,IAAI,CAACrI,QAAQ,GAAGA,QAAQ;QACxB,IAAI,CAACqI,gBAAgB,GAAGA,gBAAgB;MAC1C;MACAnI,gBAAgBA,CAACkC,GAAG,EAAE;QACpB,OAAO,IAAI,CAACiG,gBAAgB,CAAC/H,IAAI,CAACqB,KAAK,CAAC,CAACS,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;MAC1D;IACF,CAAC;IACD,IAAIkE,IAAI,GAAG8B,KAAK;IAChB9B,IAAI,CAACO,OAAO,GAAG,IAAIuB,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACzC9B,IAAI,CAACa,YAAY,GAAG,IAAIiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC7C9B,IAAI,CAACC,IAAI,GAAG,IAAI6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACrC9B,IAAI,CAACgC,KAAK,GAAG,IAAIF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACtC9B,IAAI,CAACiB,GAAG,GAAG,IAAIa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClCD,UAAU,CAAC7B,IAAI,GAAGA,IAAI;EACxB,CAAC,EAAE7H,SAAS,GAAGhC,UAAU,CAACgC,SAAS,KAAKhC,UAAU,CAACgC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC,EAAEjC,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI+L,iBAAiB,GAAG/L,SAAS;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgM,eAAe,GAAG;EACpBC,CAAC,EAAEF,iBAAiB,CAACvC,MAAM,CAACvG,GAAG,CAACyI,GAAG;EACnCQ,CAAC,EAAEH,iBAAiB,CAACvC,MAAM,CAACvG,GAAG,CAACC,MAAM;EACtCiJ,CAAC,EAAEJ,iBAAiB,CAACvC,MAAM,CAACvG,GAAG,CAACE,QAAQ;EACxCiJ,CAAC,EAAEL,iBAAiB,CAACvC,MAAM,CAACvG,GAAG,CAACG;AAClC,CAAC;AACD,IAAIiJ,YAAY,GAAG,GAAG;AACtB,IAAIC,aAAa,GAAG,GAAG;AACvB,IAAIC,eAAe,GAAG,SAAS;AAC/B,IAAIC,eAAe,GAAG,SAAS;AAC/B,IAAIC,qBAAqB,GAAG,KAAK;AACjC,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,iBAAiB,GAAG,GAAG;AAC3B,SAASC,YAAYA,CAACpM,OAAO,EAAEqM,MAAM,GAAG,CAAC,EAAE;EACzC,MAAMC,GAAG,GAAG,EAAE;EACdtM,OAAO,CAACyD,OAAO,CAAC,UAASnD,GAAG,EAAEsD,CAAC,EAAE;IAC/B,IAAI2I,KAAK,GAAG,IAAI;IAChBjM,GAAG,CAACmD,OAAO,CAAC,UAAS+I,IAAI,EAAE7I,CAAC,EAAE;MAC5B,IAAI,CAAC6I,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QAC3BD,GAAG,CAAC9L,IAAI,CAAC,IAAI+L,KAAK,GAAGF,MAAM,IAAIzI,CAAC,GAAGyI,MAAM,IAAI1I,CAAC,GAAG4I,KAAK,MAAMA,KAAK,GAAGF,MAAM,GAAG,CAAC;QAC9EE,KAAK,GAAG,IAAI;QACZ;MACF;MACA,IAAI5I,CAAC,KAAKrD,GAAG,CAAC8C,MAAM,GAAG,CAAC,EAAE;QACxB,IAAI,CAACoJ,IAAI,EAAE;UACT;QACF;QACA,IAAID,KAAK,KAAK,IAAI,EAAE;UAClBD,GAAG,CAAC9L,IAAI,CAAC,IAAImD,CAAC,GAAG0I,MAAM,IAAIzI,CAAC,GAAGyI,MAAM,SAAS1I,CAAC,GAAG0I,MAAM,GAAG,CAAC;QAC9D,CAAC,MAAM;UACLC,GAAG,CAAC9L,IAAI,CAAC,IAAI+L,KAAK,GAAGF,MAAM,IAAIzI,CAAC,GAAGyI,MAAM,KAAK1I,CAAC,GAAG,CAAC,GAAG4I,KAAK,MAAMA,KAAK,GAAGF,MAAM,GAAG,CAAC;QACrF;QACA;MACF;MACA,IAAIG,IAAI,IAAID,KAAK,KAAK,IAAI,EAAE;QAC1BA,KAAK,GAAG5I,CAAC;MACX;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO2I,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACrB;AACA,SAASC,eAAeA,CAAC1M,OAAO,EAAE2M,UAAU,EAAE;EAC5C,OAAO3M,OAAO,CAACS,KAAK,CAAC,CAAC,CAAC2H,GAAG,CAAC,CAAC9H,GAAG,EAAEsD,CAAC,KAAK;IACrC,IAAIA,CAAC,GAAG+I,UAAU,CAAC/I,CAAC,IAAIA,CAAC,IAAI+I,UAAU,CAAC/I,CAAC,GAAG+I,UAAU,CAACC,CAAC,EAAE;MACxD,OAAOtM,GAAG;IACZ;IACA,OAAOA,GAAG,CAAC8H,GAAG,CAAC,CAACoE,IAAI,EAAE7I,CAAC,KAAK;MAC1B,IAAIA,CAAC,GAAGgJ,UAAU,CAAChJ,CAAC,IAAIA,CAAC,IAAIgJ,UAAU,CAAChJ,CAAC,GAAGgJ,UAAU,CAACE,CAAC,EAAE;QACxD,OAAOL,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASM,gBAAgBA,CAACC,KAAK,EAAE1M,IAAI,EAAE2M,aAAa,EAAEC,aAAa,EAAE;EACnE,IAAIA,aAAa,IAAI,IAAI,EAAE;IACzB,OAAO,IAAI;EACb;EACA,MAAMZ,MAAM,GAAGW,aAAa,GAAGd,WAAW,GAAG,CAAC;EAC9C,MAAMgB,QAAQ,GAAGH,KAAK,CAAC3J,MAAM,GAAGiJ,MAAM,GAAG,CAAC;EAC1C,MAAMc,WAAW,GAAG7J,IAAI,CAACqB,KAAK,CAACtE,IAAI,GAAG8L,iBAAiB,CAAC;EACxD,MAAMiB,KAAK,GAAGF,QAAQ,GAAG7M,IAAI;EAC7B,MAAMwM,CAAC,GAAG,CAACI,aAAa,CAACI,KAAK,IAAIF,WAAW,IAAIC,KAAK;EACtD,MAAMR,CAAC,GAAG,CAACK,aAAa,CAACK,MAAM,IAAIH,WAAW,IAAIC,KAAK;EACvD,MAAMzJ,CAAC,GAAGsJ,aAAa,CAACtJ,CAAC,IAAI,IAAI,GAAGoJ,KAAK,CAAC3J,MAAM,GAAG,CAAC,GAAGyJ,CAAC,GAAG,CAAC,GAAGI,aAAa,CAACtJ,CAAC,GAAGyJ,KAAK;EACtF,MAAMxJ,CAAC,GAAGqJ,aAAa,CAACrJ,CAAC,IAAI,IAAI,GAAGmJ,KAAK,CAAC3J,MAAM,GAAG,CAAC,GAAGwJ,CAAC,GAAG,CAAC,GAAGK,aAAa,CAACrJ,CAAC,GAAGwJ,KAAK;EACtF,IAAIT,UAAU,GAAG,IAAI;EACrB,IAAIM,aAAa,CAACM,QAAQ,EAAE;IAC1B,IAAIC,MAAM,GAAGlK,IAAI,CAACqB,KAAK,CAAChB,CAAC,CAAC;IAC1B,IAAI8J,MAAM,GAAGnK,IAAI,CAACqB,KAAK,CAACf,CAAC,CAAC;IAC1B,IAAI8J,KAAK,GAAGpK,IAAI,CAACqE,IAAI,CAACkF,CAAC,GAAGlJ,CAAC,GAAG6J,MAAM,CAAC;IACrC,IAAIG,KAAK,GAAGrK,IAAI,CAACqE,IAAI,CAACiF,CAAC,GAAGhJ,CAAC,GAAG6J,MAAM,CAAC;IACrCd,UAAU,GAAG;MAAEhJ,CAAC,EAAE6J,MAAM;MAAE5J,CAAC,EAAE6J,MAAM;MAAEZ,CAAC,EAAEa,KAAK;MAAEd,CAAC,EAAEe;IAAM,CAAC;EAC3D;EACA,OAAO;IAAEhK,CAAC;IAAEC,CAAC;IAAEgJ,CAAC;IAAEC,CAAC;IAAEF;EAAW,CAAC;AACnC;AACA,IAAIiB,eAAe,GAAG,YAAW;EAC/B,IAAI;IACF,IAAIC,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,IAAID,MAAM,CAAC,CAAC,CAAC;EACpC,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC,CAAC,CAAC;AACH,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,EAAE,GAAGD,KAAK;IAAE;MAChBxP,KAAK;MACL4B,IAAI,GAAGwL,YAAY;MACnBsC,KAAK,GAAGrC,aAAa;MACrBsC,OAAO,GAAGrC,eAAe;MACzBsC,OAAO,GAAGrC,eAAe;MACzBgB,aAAa,GAAGf,qBAAqB;MACrCqC,KAAK;MACLrB;IACF,CAAC,GAAGiB,EAAE;IAAEK,UAAU,GAAGrP,SAAS,CAACgP,EAAE,EAAE,CACjC,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,OAAO,EACP,eAAe,CAChB,CAAC;EACF,MAAMM,MAAM,GAAGvB,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACwB,GAAG;EACjE,MAAMC,OAAO,GAAGnP,KAAK,CAACoP,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,MAAM,GAAGrP,KAAK,CAACoP,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACE,WAAW,EAAEC,gBAAgB,CAAC,GAAGvP,KAAK,CAACwP,QAAQ,CAAC,KAAK,CAAC;EAC7DxP,KAAK,CAACyP,SAAS,CAAC,MAAM;IACpB,IAAIN,OAAO,CAACO,OAAO,IAAI,IAAI,EAAE;MAC3B,MAAMC,MAAM,GAAGR,OAAO,CAACO,OAAO;MAC9B,MAAME,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MACnC,IAAI,CAACD,GAAG,EAAE;QACR;MACF;MACA,IAAIpC,KAAK,GAAGxB,iBAAiB,CAACvC,MAAM,CAAC3H,UAAU,CAAC5C,KAAK,EAAE+M,eAAe,CAAC2C,KAAK,CAAC,CAAC,CAACtK,UAAU,CAAC,CAAC;MAC3F,MAAMwI,MAAM,GAAGW,aAAa,GAAGd,WAAW,GAAG,CAAC;MAC9C,MAAMgB,QAAQ,GAAGH,KAAK,CAAC3J,MAAM,GAAGiJ,MAAM,GAAG,CAAC;MAC1C,MAAMgD,uBAAuB,GAAGvC,gBAAgB,CAACC,KAAK,EAAE1M,IAAI,EAAE2M,aAAa,EAAEC,aAAa,CAAC;MAC3F,MAAMqC,KAAK,GAAGV,MAAM,CAACK,OAAO;MAC5B,MAAMM,iBAAiB,GAAGF,uBAAuB,IAAI,IAAI,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,aAAa,KAAK,CAAC,IAAIH,KAAK,CAACI,YAAY,KAAK,CAAC;MACtJ,IAAIH,iBAAiB,EAAE;QACrB,IAAIF,uBAAuB,CAAC1C,UAAU,IAAI,IAAI,EAAE;UAC9CI,KAAK,GAAGL,eAAe,CAACK,KAAK,EAAEsC,uBAAuB,CAAC1C,UAAU,CAAC;QACpE;MACF;MACA,MAAMgD,UAAU,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC;MAC/CX,MAAM,CAAC5B,MAAM,GAAG4B,MAAM,CAAC7B,KAAK,GAAGhN,IAAI,GAAGsP,UAAU;MAChD,MAAMvC,KAAK,GAAG/M,IAAI,GAAG6M,QAAQ,GAAGyC,UAAU;MAC1CR,GAAG,CAAC/B,KAAK,CAACA,KAAK,EAAEA,KAAK,CAAC;MACvB+B,GAAG,CAACW,SAAS,GAAG1B,OAAO;MACvBe,GAAG,CAACY,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE7C,QAAQ,EAAEA,QAAQ,CAAC;MACtCiC,GAAG,CAACW,SAAS,GAAGzB,OAAO;MACvB,IAAIT,eAAe,EAAE;QACnBuB,GAAG,CAACa,IAAI,CAAC,IAAInC,MAAM,CAACzB,YAAY,CAACW,KAAK,EAAEV,MAAM,CAAC,CAAC,CAAC;MACnD,CAAC,MAAM;QACLU,KAAK,CAACtJ,OAAO,CAAC,UAASnD,GAAG,EAAE2P,GAAG,EAAE;UAC/B3P,GAAG,CAACmD,OAAO,CAAC,UAAS+I,IAAI,EAAE0D,GAAG,EAAE;YAC9B,IAAI1D,IAAI,EAAE;cACR2C,GAAG,CAACY,QAAQ,CAACG,GAAG,GAAG7D,MAAM,EAAE4D,GAAG,GAAG5D,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC;YAChD;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MACA,IAAIkD,iBAAiB,EAAE;QACrBJ,GAAG,CAACgB,SAAS,CAACb,KAAK,EAAED,uBAAuB,CAAC1L,CAAC,GAAG0I,MAAM,EAAEgD,uBAAuB,CAACzL,CAAC,GAAGyI,MAAM,EAAEgD,uBAAuB,CAACxC,CAAC,EAAEwC,uBAAuB,CAACzC,CAAC,CAAC;MACpJ;IACF;EACF,CAAC,CAAC;EACFrN,KAAK,CAACyP,SAAS,CAAC,MAAM;IACpBF,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;EACZ,MAAM4B,WAAW,GAAGvR,cAAc,CAAC;IAAEyO,MAAM,EAAEjN,IAAI;IAAEgN,KAAK,EAAEhN;EAAK,CAAC,EAAEiO,KAAK,CAAC;EACxE,IAAI+B,GAAG,GAAG,IAAI;EACd,IAAI7B,MAAM,IAAI,IAAI,EAAE;IAClB6B,GAAG,GAAG,eAAgB9Q,KAAK,CAAC+Q,aAAa,CAAC,KAAK,EAAE;MAC/C7B,GAAG,EAAED,MAAM;MACXhQ,GAAG,EAAEgQ,MAAM;MACXF,KAAK,EAAE;QAAEiC,OAAO,EAAE;MAAO,CAAC;MAC1BC,MAAM,EAAEA,CAAA,KAAM;QACZ1B,gBAAgB,CAAC,IAAI,CAAC;MACxB,CAAC;MACD2B,GAAG,EAAE7B;IACP,CAAC,CAAC;EACJ;EACA,OAAO,eAAgBrP,KAAK,CAAC+Q,aAAa,CAAC/Q,KAAK,CAACmR,QAAQ,EAAE,IAAI,EAAE,eAAgBnR,KAAK,CAAC+Q,aAAa,CAAC,QAAQ,EAAEzR,cAAc,CAAC;IAC5HyP,KAAK,EAAE8B,WAAW;IAClB9C,MAAM,EAAEjN,IAAI;IACZgN,KAAK,EAAEhN,IAAI;IACXoQ,GAAG,EAAE/B;EACP,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAE8B,GAAG,CAAC;AACvB;AACA,SAASM,SAASA,CAAC1C,KAAK,EAAE;EACxB,MAAMC,EAAE,GAAGD,KAAK;IAAE;MAChBxP,KAAK;MACL4B,IAAI,GAAGwL,YAAY;MACnBsC,KAAK,GAAGrC,aAAa;MACrBsC,OAAO,GAAGrC,eAAe;MACzBsC,OAAO,GAAGrC,eAAe;MACzBgB,aAAa,GAAGf,qBAAqB;MACrCgB;IACF,CAAC,GAAGiB,EAAE;IAAEK,UAAU,GAAGrP,SAAS,CAACgP,EAAE,EAAE,CACjC,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,eAAe,CAChB,CAAC;EACF,IAAInB,KAAK,GAAGxB,iBAAiB,CAACvC,MAAM,CAAC3H,UAAU,CAAC5C,KAAK,EAAE+M,eAAe,CAAC2C,KAAK,CAAC,CAAC,CAACtK,UAAU,CAAC,CAAC;EAC3F,MAAMwI,MAAM,GAAGW,aAAa,GAAGd,WAAW,GAAG,CAAC;EAC9C,MAAMgB,QAAQ,GAAGH,KAAK,CAAC3J,MAAM,GAAGiJ,MAAM,GAAG,CAAC;EAC1C,MAAMgD,uBAAuB,GAAGvC,gBAAgB,CAACC,KAAK,EAAE1M,IAAI,EAAE2M,aAAa,EAAEC,aAAa,CAAC;EAC3F,IAAIqC,KAAK,GAAG,IAAI;EAChB,IAAIrC,aAAa,IAAI,IAAI,IAAIoC,uBAAuB,IAAI,IAAI,EAAE;IAC5D,IAAIA,uBAAuB,CAAC1C,UAAU,IAAI,IAAI,EAAE;MAC9CI,KAAK,GAAGL,eAAe,CAACK,KAAK,EAAEsC,uBAAuB,CAAC1C,UAAU,CAAC;IACpE;IACA2C,KAAK,GAAG,eAAgB/P,KAAK,CAAC+Q,aAAa,CAAC,OAAO,EAAE;MACnDM,SAAS,EAAE3D,aAAa,CAACwB,GAAG;MAC5BnB,MAAM,EAAE+B,uBAAuB,CAACzC,CAAC;MACjCS,KAAK,EAAEgC,uBAAuB,CAACxC,CAAC;MAChClJ,CAAC,EAAE0L,uBAAuB,CAAC1L,CAAC,GAAG0I,MAAM;MACrCzI,CAAC,EAAEyL,uBAAuB,CAACzL,CAAC,GAAGyI,MAAM;MACrCwE,mBAAmB,EAAE;IACvB,CAAC,CAAC;EACJ;EACA,MAAMC,MAAM,GAAG1E,YAAY,CAACW,KAAK,EAAEV,MAAM,CAAC;EAC1C,OAAO,eAAgB9M,KAAK,CAAC+Q,aAAa,CAAC,KAAK,EAAEzR,cAAc,CAAC;IAC/DyO,MAAM,EAAEjN,IAAI;IACZgN,KAAK,EAAEhN,IAAI;IACX0Q,OAAO,EAAE,OAAO7D,QAAQ,IAAIA,QAAQ;EACtC,CAAC,EAAEqB,UAAU,CAAC,EAAE,eAAgBhP,KAAK,CAAC+Q,aAAa,CAAC,MAAM,EAAE;IAC1DN,IAAI,EAAE5B,OAAO;IACb4C,CAAC,EAAE,SAAS9D,QAAQ,IAAIA,QAAQ,KAAK;IACrC+D,cAAc,EAAE;EAClB,CAAC,CAAC,EAAE,eAAgB1R,KAAK,CAAC+Q,aAAa,CAAC,MAAM,EAAE;IAC9CN,IAAI,EAAE3B,OAAO;IACb2C,CAAC,EAAEF,MAAM;IACTG,cAAc,EAAE;EAClB,CAAC,CAAC,EAAE3B,KAAK,CAAC;AACZ;AACA,IAAI4B,MAAM,GAAIjD,KAAK,IAAK;EACtB,MAAMC,EAAE,GAAGD,KAAK;IAAE;MAAEkD;IAAS,CAAC,GAAGjD,EAAE;IAAEK,UAAU,GAAGrP,SAAS,CAACgP,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EAC7E,IAAIiD,QAAQ,KAAK,KAAK,EAAE;IACtB,OAAO,eAAgB5R,KAAK,CAAC+Q,aAAa,CAACK,SAAS,EAAE9R,cAAc,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,CAAC;EACvF;EACA,OAAO,eAAgBhP,KAAK,CAAC+Q,aAAa,CAACtC,YAAY,EAAEnP,cAAc,CAAC,CAAC,CAAC,EAAE0P,UAAU,CAAC,CAAC;AAC1F,CAAC;AACD,SACEP,YAAY,EACZ2C,SAAS,EACTO,MAAM,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}