[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\ModeSelector.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\QuizPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\PollPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreateQuiz.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreatePoll.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\socket.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\HomePage.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\AdminDashboard.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\ParticipantDashboard.js": "11"}, {"size": 252, "mtime": 1748781960604, "results": "12", "hashOfConfig": "13"}, {"size": 1876, "mtime": 1748783389376, "results": "14", "hashOfConfig": "13"}, {"size": 803, "mtime": 1748782208737, "results": "15", "hashOfConfig": "13"}, {"size": 3853, "mtime": 1748782264104, "results": "16", "hashOfConfig": "13"}, {"size": 3345, "mtime": 1748782249521, "results": "17", "hashOfConfig": "13"}, {"size": 4217, "mtime": 1748782234937, "results": "18", "hashOfConfig": "13"}, {"size": 6100, "mtime": 1748783549014, "results": "19", "hashOfConfig": "13"}, {"size": 108, "mtime": 1748781955371, "results": "20", "hashOfConfig": "13"}, {"size": 1987, "mtime": 1748783404922, "results": "21", "hashOfConfig": "13"}, {"size": 7549, "mtime": 1748783437621, "results": "22", "hashOfConfig": "13"}, {"size": 5060, "mtime": 1748783463492, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "w8srxg", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\ModeSelector.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\QuizPage.js", ["57"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\PollPage.js", ["58"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreateQuiz.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreatePoll.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\socket.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\HomePage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\AdminDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\ParticipantDashboard.js", [], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 28, "column": 6, "nodeType": "61", "endLine": 28, "endColumn": 10, "suggestions": "62"}, {"ruleId": "59", "severity": 1, "message": "63", "line": 27, "column": 6, "nodeType": "61", "endLine": 27, "endColumn": 10, "suggestions": "64"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchQuiz'. Either include it or remove the dependency array.", "ArrayExpression", ["65"], "React Hook useEffect has a missing dependency: 'fetchPoll'. Either include it or remove the dependency array.", ["66"], {"desc": "67", "fix": "68"}, {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [fetchQuiz, id]", {"range": "71", "text": "72"}, "Update the dependencies array to be: [fetchPoll, id]", {"range": "73", "text": "74"}, [784, 788], "[fetchQuiz, id]", [726, 730], "[fetchPoll, id]"]