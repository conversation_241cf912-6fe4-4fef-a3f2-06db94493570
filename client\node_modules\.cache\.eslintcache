[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\ModeSelector.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\QuizPage.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\PollPage.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreateQuiz.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreatePoll.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\socket.js": "8"}, {"size": 252, "mtime": 1748781960604, "results": "9", "hashOfConfig": "10"}, {"size": 683, "mtime": 1748782200417, "results": "11", "hashOfConfig": "10"}, {"size": 803, "mtime": 1748782208737, "results": "12", "hashOfConfig": "10"}, {"size": 3853, "mtime": 1748782264104, "results": "13", "hashOfConfig": "10"}, {"size": 3345, "mtime": 1748782249521, "results": "14", "hashOfConfig": "10"}, {"size": 4217, "mtime": 1748782234937, "results": "15", "hashOfConfig": "10"}, {"size": 3263, "mtime": 1748782221168, "results": "16", "hashOfConfig": "10"}, {"size": 108, "mtime": 1748781955371, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "w8srxg", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\ModeSelector.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\QuizPage.js", ["42"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\pages\\PollPage.js", ["43"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreateQuiz.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\components\\CreatePoll.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\livepoll_and_quizapp\\client\\src\\socket.js", [], [], {"ruleId": "44", "severity": 1, "message": "45", "line": 28, "column": 6, "nodeType": "46", "endLine": 28, "endColumn": 10, "suggestions": "47"}, {"ruleId": "44", "severity": 1, "message": "48", "line": 27, "column": 6, "nodeType": "46", "endLine": 27, "endColumn": 10, "suggestions": "49"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchQuiz'. Either include it or remove the dependency array.", "ArrayExpression", ["50"], "React Hook useEffect has a missing dependency: 'fetchPoll'. Either include it or remove the dependency array.", ["51"], {"desc": "52", "fix": "53"}, {"desc": "54", "fix": "55"}, "Update the dependencies array to be: [fetchQuiz, id]", {"range": "56", "text": "57"}, "Update the dependencies array to be: [fetchPoll, id]", {"range": "58", "text": "59"}, [784, 788], "[fetchQuiz, id]", [726, 730], "[fetchPoll, id]"]