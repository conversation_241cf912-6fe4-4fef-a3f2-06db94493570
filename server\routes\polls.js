const express = require('express');
const router = express.Router();
const Poll = require('../models/Poll');

// Create a new poll
router.post('/', async (req, res) => {
  try {
    const { question, options } = req.body;
    
    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({ 
        error: 'Question is required and must have 2-5 options' 
      });
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      voteCount: 0
    }));

    const poll = new Poll({
      question,
      options: formattedOptions
    });

    const savedPoll = await poll.save();
    res.status(201).json(savedPoll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get a specific poll
router.get('/:id', async (req, res) => {
  try {
    const poll = await Poll.findById(req.params.id);
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }
    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Vote on a poll
router.post('/:id/vote', async (req, res) => {
  try {
    const { optionIndex } = req.body;
    const poll = await Poll.findById(req.params.id);
    
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    if (optionIndex < 0 || optionIndex >= poll.options.length) {
      return res.status(400).json({ error: 'Invalid option index' });
    }

    poll.options[optionIndex].voteCount += 1;
    await poll.save();

    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
