const express = require('express');
const router = express.Router();
const Poll = require('../models/Poll');
const QRCode = require('qrcode');

// Generate unique codes
function generateCode(length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// Create a new poll
router.post('/', async (req, res) => {
  try {
    const { question, options } = req.body;

    // Validation
    if (!question || !options || options.length < 2 || options.length > 5) {
      return res.status(400).json({
        error: 'Question is required and must have 2-5 options'
      });
    }

    // Generate unique codes
    let adminCode, participantCode;
    let isUnique = false;

    while (!isUnique) {
      adminCode = generateCode();
      participantCode = generateCode();

      const existingPoll = await Poll.findOne({
        $or: [{ adminCode }, { participantCode }]
      });

      if (!existingPoll) {
        isUnique = true;
      }
    }

    // Format options for database
    const formattedOptions = options.map(option => ({
      text: option,
      voteCount: 0
    }));

    const poll = new Poll({
      question,
      options: formattedOptions,
      adminCode,
      participantCode,
      totalVotes: 0
    });

    const savedPoll = await poll.save();

    // Generate QR codes
    const participantUrl = `${req.protocol}://${req.get('host')}/participate/poll/${participantCode}`;
    const adminUrl = `${req.protocol}://${req.get('host')}/admin/poll/${adminCode}`;

    const participantQR = await QRCode.toDataURL(participantUrl);
    const adminQR = await QRCode.toDataURL(adminUrl);

    res.status(201).json({
      ...savedPoll.toObject(),
      participantQR,
      adminQR,
      participantUrl,
      adminUrl
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get poll by participant code
router.get('/participate/:code', async (req, res) => {
  try {
    const poll = await Poll.findOne({ participantCode: req.params.code, isActive: true });
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found or inactive' });
    }
    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get poll by admin code
router.get('/admin/:code', async (req, res) => {
  try {
    const poll = await Poll.findOne({ adminCode: req.params.code });
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }
    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get all polls for admin dashboard
router.get('/admin/all/polls', async (req, res) => {
  try {
    const polls = await Poll.find().sort({ createdAt: -1 });
    res.json(polls);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Vote on a poll
router.post('/participate/:code/vote', async (req, res) => {
  try {
    const { optionIndex } = req.body;
    const poll = await Poll.findOne({ participantCode: req.params.code, isActive: true });

    if (!poll) {
      return res.status(404).json({ error: 'Poll not found or inactive' });
    }

    if (optionIndex < 0 || optionIndex >= poll.options.length) {
      return res.status(400).json({ error: 'Invalid option index' });
    }

    poll.options[optionIndex].voteCount += 1;
    poll.totalVotes += 1;
    await poll.save();

    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Toggle poll status (admin only)
router.patch('/admin/:code/toggle', async (req, res) => {
  try {
    const poll = await Poll.findOne({ adminCode: req.params.code });
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }

    poll.isActive = !poll.isActive;
    await poll.save();

    res.json(poll);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete poll (admin only)
router.delete('/admin/:code', async (req, res) => {
  try {
    const poll = await Poll.findOneAndDelete({ adminCode: req.params.code });
    if (!poll) {
      return res.status(404).json({ error: 'Poll not found' });
    }
    res.json({ message: 'Poll deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
