import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';

function AdminDashboard() {
  const [polls, setPolls] = useState([]);
  const [quizzes, setQuizzes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [pollsResponse, quizzesResponse] = await Promise.all([
        axios.get('/api/polls/admin/all/polls'),
        axios.get('/api/quizzes/admin/all/quizzes')
      ]);
      
      setPolls(pollsResponse.data);
      setQuizzes(quizzesResponse.data);
    } catch (err) {
      setError('Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const togglePollStatus = async (adminCode) => {
    try {
      await axios.patch(`/api/polls/admin/${adminCode}/toggle`);
      fetchData(); // Refresh data
    } catch (err) {
      setError('Failed to toggle poll status');
    }
  };

  const toggleQuizStatus = async (adminCode) => {
    try {
      await axios.patch(`/api/quizzes/admin/${adminCode}/toggle`);
      fetchData(); // Refresh data
    } catch (err) {
      setError('Failed to toggle quiz status');
    }
  };

  const deletePoll = async (adminCode) => {
    if (window.confirm('Are you sure you want to delete this poll?')) {
      try {
        await axios.delete(`/api/polls/admin/${adminCode}`);
        fetchData(); // Refresh data
      } catch (err) {
        setError('Failed to delete poll');
      }
    }
  };

  const deleteQuiz = async (adminCode) => {
    if (window.confirm('Are you sure you want to delete this quiz?')) {
      try {
        await axios.delete(`/api/quizzes/admin/${adminCode}`);
        fetchData(); // Refresh data
      } catch (err) {
        setError('Failed to delete quiz');
      }
    }
  };

  if (loading) return <div className="loading">Loading dashboard...</div>;

  return (
    <div className="admin-dashboard">
      <div className="dashboard-header">
        <h2>Admin Dashboard</h2>
        <div className="create-buttons">
          <Link to="/admin/create-poll" className="create-button poll">
            📊 Create New Poll
          </Link>
          <Link to="/admin/create-quiz" className="create-button quiz">
            🧠 Create New Quiz
          </Link>
        </div>
      </div>

      {error && <div className="error">{error}</div>}

      <div className="dashboard-grid">
        <div className="dashboard-section">
          <h3>📊 Active Polls ({polls.filter(p => p.isActive).length})</h3>
          <div className="items-grid">
            {polls.map(poll => (
              <div key={poll._id} className={`item-card ${poll.isActive ? 'active' : 'inactive'}`}>
                <div className="item-header">
                  <h4>{poll.question}</h4>
                  <div className="item-status">
                    <span className={`status-badge ${poll.isActive ? 'active' : 'inactive'}`}>
                      {poll.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                
                <div className="item-stats">
                  <div className="stat">
                    <span className="stat-number">{poll.totalVotes}</span>
                    <span className="stat-label">Total Votes</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">{poll.options.length}</span>
                    <span className="stat-label">Options</span>
                  </div>
                </div>

                <div className="item-codes">
                  <div className="code-item">
                    <span className="code-label">Admin Code:</span>
                    <span className="code-value">{poll.adminCode}</span>
                  </div>
                  <div className="code-item">
                    <span className="code-label">Participant Code:</span>
                    <span className="code-value">{poll.participantCode}</span>
                  </div>
                </div>

                <div className="item-actions">
                  <Link to={`/admin/poll/${poll.adminCode}`} className="action-button view">
                    View Results
                  </Link>
                  <button 
                    onClick={() => togglePollStatus(poll.adminCode)}
                    className={`action-button ${poll.isActive ? 'deactivate' : 'activate'}`}
                  >
                    {poll.isActive ? 'Deactivate' : 'Activate'}
                  </button>
                  <button 
                    onClick={() => deletePoll(poll.adminCode)}
                    className="action-button delete"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="dashboard-section">
          <h3>🧠 Active Quizzes ({quizzes.filter(q => q.isActive).length})</h3>
          <div className="items-grid">
            {quizzes.map(quiz => (
              <div key={quiz._id} className={`item-card ${quiz.isActive ? 'active' : 'inactive'}`}>
                <div className="item-header">
                  <h4>{quiz.question}</h4>
                  <div className="item-status">
                    <span className={`status-badge ${quiz.isActive ? 'active' : 'inactive'}`}>
                      {quiz.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                
                <div className="item-stats">
                  <div className="stat">
                    <span className="stat-number">{quiz.totalAnswers}</span>
                    <span className="stat-label">Total Answers</span>
                  </div>
                  <div className="stat">
                    <span className="stat-number">{quiz.options.length}</span>
                    <span className="stat-label">Options</span>
                  </div>
                </div>

                <div className="item-codes">
                  <div className="code-item">
                    <span className="code-label">Admin Code:</span>
                    <span className="code-value">{quiz.adminCode}</span>
                  </div>
                  <div className="code-item">
                    <span className="code-label">Participant Code:</span>
                    <span className="code-value">{quiz.participantCode}</span>
                  </div>
                </div>

                <div className="item-actions">
                  <Link to={`/admin/quiz/${quiz.adminCode}`} className="action-button view">
                    View Results
                  </Link>
                  <button 
                    onClick={() => toggleQuizStatus(quiz.adminCode)}
                    className={`action-button ${quiz.isActive ? 'deactivate' : 'activate'}`}
                  >
                    {quiz.isActive ? 'Deactivate' : 'Activate'}
                  </button>
                  <button 
                    onClick={() => deleteQuiz(quiz.adminCode)}
                    className="action-button delete"
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
