{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\livepoll_and_quizapp\\\\client\\\\src\\\\components\\\\CreateQuiz.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CreateQuiz() {\n  _s();\n  const [question, setQuestion] = useState('');\n  const [options, setOptions] = useState(['', '']);\n  const [correctAnswer, setCorrectAnswer] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleOptionChange = (index, value) => {\n    const newOptions = [...options];\n    newOptions[index] = value;\n    setOptions(newOptions);\n  };\n  const addOption = () => {\n    if (options.length < 5) {\n      setOptions([...options, '']);\n    }\n  };\n  const removeOption = index => {\n    if (options.length > 2) {\n      const newOptions = options.filter((_, i) => i !== index);\n      setOptions(newOptions);\n\n      // Adjust correct answer if necessary\n      if (correctAnswer >= newOptions.length) {\n        setCorrectAnswer(0);\n      } else if (correctAnswer > index) {\n        setCorrectAnswer(correctAnswer - 1);\n      }\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n\n    // Validation\n    if (!question.trim()) {\n      setError('Question is required');\n      return;\n    }\n    const validOptions = options.filter(option => option.trim() !== '');\n    if (validOptions.length < 2) {\n      setError('At least 2 options are required');\n      return;\n    }\n    if (correctAnswer >= validOptions.length) {\n      setError('Please select a valid correct answer');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post('/api/quizzes', {\n        question: question.trim(),\n        options: validOptions,\n        correctAnswer\n      });\n      navigate(`/quiz/${response.data._id}`);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Failed to create quiz');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Create a Quiz\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"question\",\n          children: \"Question:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"question\",\n          value: question,\n          onChange: e => setQuestion(e.target.value),\n          placeholder: \"Enter your quiz question\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Options:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: option,\n            onChange: e => handleOptionChange(index, e.target.value),\n            placeholder: `Option ${index + 1}`,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this), options.length > 2 && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => removeOption(index),\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this)), options.length < 5 && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"add-option-btn\",\n          onClick: addOption,\n          children: \"Add Option\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"correct-answer-selector\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"correctAnswer\",\n          children: \"Correct Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          id: \"correctAnswer\",\n          value: correctAnswer,\n          onChange: e => setCorrectAnswer(parseInt(e.target.value)),\n          children: options.map((option, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: index,\n            children: [\"Option \", index + 1, \": \", option || `Option ${index + 1}`]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"submit-btn\",\n        disabled: loading,\n        children: loading ? 'Creating Quiz...' : 'Create Quiz'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateQuiz, \"oLOpw8zRtxnzsYQt7Xa8tEt19Gw=\", false, function () {\n  return [useNavigate];\n});\n_c = CreateQuiz;\nexport default CreateQuiz;\nvar _c;\n$RefreshReg$(_c, \"CreateQuiz\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "jsxDEV", "_jsxDEV", "CreateQuiz", "_s", "question", "setQuestion", "options", "setOptions", "<PERSON><PERSON><PERSON><PERSON>", "setCorrectAnswer", "loading", "setLoading", "error", "setError", "navigate", "handleOptionChange", "index", "value", "newOptions", "addOption", "length", "removeOption", "filter", "_", "i", "handleSubmit", "e", "preventDefault", "trim", "validOptions", "option", "response", "post", "data", "_id", "err", "_err$response", "_err$response$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "target", "placeholder", "required", "map", "onClick", "parseInt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/src/components/CreateQuiz.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\n\nfunction CreateQuiz() {\n  const [question, setQuestion] = useState('');\n  const [options, setOptions] = useState(['', '']);\n  const [correctAnswer, setCorrectAnswer] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleOptionChange = (index, value) => {\n    const newOptions = [...options];\n    newOptions[index] = value;\n    setOptions(newOptions);\n  };\n\n  const addOption = () => {\n    if (options.length < 5) {\n      setOptions([...options, '']);\n    }\n  };\n\n  const removeOption = (index) => {\n    if (options.length > 2) {\n      const newOptions = options.filter((_, i) => i !== index);\n      setOptions(newOptions);\n      \n      // Adjust correct answer if necessary\n      if (correctAnswer >= newOptions.length) {\n        setCorrectAnswer(0);\n      } else if (correctAnswer > index) {\n        setCorrectAnswer(correctAnswer - 1);\n      }\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    \n    // Validation\n    if (!question.trim()) {\n      setError('Question is required');\n      return;\n    }\n\n    const validOptions = options.filter(option => option.trim() !== '');\n    if (validOptions.length < 2) {\n      setError('At least 2 options are required');\n      return;\n    }\n\n    if (correctAnswer >= validOptions.length) {\n      setError('Please select a valid correct answer');\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      const response = await axios.post('/api/quizzes', {\n        question: question.trim(),\n        options: validOptions,\n        correctAnswer\n      });\n      \n      navigate(`/quiz/${response.data._id}`);\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to create quiz');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <h2>Create a Quiz</h2>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"question\">Question:</label>\n          <input\n            type=\"text\"\n            id=\"question\"\n            value={question}\n            onChange={(e) => setQuestion(e.target.value)}\n            placeholder=\"Enter your quiz question\"\n            required\n          />\n        </div>\n\n        <div className=\"options-container\">\n          <label>Options:</label>\n          {options.map((option, index) => (\n            <div key={index} className=\"option-input\">\n              <input\n                type=\"text\"\n                value={option}\n                onChange={(e) => handleOptionChange(index, e.target.value)}\n                placeholder={`Option ${index + 1}`}\n                required\n              />\n              {options.length > 2 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeOption(index)}\n                >\n                  Remove\n                </button>\n              )}\n            </div>\n          ))}\n          \n          {options.length < 5 && (\n            <button\n              type=\"button\"\n              className=\"add-option-btn\"\n              onClick={addOption}\n            >\n              Add Option\n            </button>\n          )}\n        </div>\n\n        <div className=\"correct-answer-selector\">\n          <label htmlFor=\"correctAnswer\">Correct Answer:</label>\n          <select\n            id=\"correctAnswer\"\n            value={correctAnswer}\n            onChange={(e) => setCorrectAnswer(parseInt(e.target.value))}\n          >\n            {options.map((option, index) => (\n              <option key={index} value={index}>\n                Option {index + 1}: {option || `Option ${index + 1}`}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        <button \n          type=\"submit\" \n          className=\"submit-btn\"\n          disabled={loading}\n        >\n          {loading ? 'Creating Quiz...' : 'Create Quiz'}\n        </button>\n      </form>\n    </div>\n  );\n}\n\nexport default CreateQuiz;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAChD,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC3C,MAAMC,UAAU,GAAG,CAAC,GAAGZ,OAAO,CAAC;IAC/BY,UAAU,CAACF,KAAK,CAAC,GAAGC,KAAK;IACzBV,UAAU,CAACW,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIb,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACtBb,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE,EAAE,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMe,YAAY,GAAIL,KAAK,IAAK;IAC9B,IAAIV,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMF,UAAU,GAAGZ,OAAO,CAACgB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKR,KAAK,CAAC;MACxDT,UAAU,CAACW,UAAU,CAAC;;MAEtB;MACA,IAAIV,aAAa,IAAIU,UAAU,CAACE,MAAM,EAAE;QACtCX,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM,IAAID,aAAa,GAAGQ,KAAK,EAAE;QAChCP,gBAAgB,CAACD,aAAa,GAAG,CAAC,CAAC;MACrC;IACF;EACF,CAAC;EAED,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBd,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAI,CAACT,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MACpBf,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,MAAMgB,YAAY,GAAGvB,OAAO,CAACgB,MAAM,CAACQ,MAAM,IAAIA,MAAM,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IACnE,IAAIC,YAAY,CAACT,MAAM,GAAG,CAAC,EAAE;MAC3BP,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEA,IAAIL,aAAa,IAAIqB,YAAY,CAACT,MAAM,EAAE;MACxCP,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAMjC,KAAK,CAACkC,IAAI,CAAC,cAAc,EAAE;QAChD5B,QAAQ,EAAEA,QAAQ,CAACwB,IAAI,CAAC,CAAC;QACzBtB,OAAO,EAAEuB,YAAY;QACrBrB;MACF,CAAC,CAAC;MAEFM,QAAQ,CAAC,SAASiB,QAAQ,CAACE,IAAI,CAACC,GAAG,EAAE,CAAC;IACxC,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZxB,QAAQ,CAAC,EAAAuB,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoBzB,KAAK,KAAI,uBAAuB,CAAC;IAChE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKqC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtC,OAAA;MAAAsC,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAErB/B,KAAK,iBAAIX,OAAA;MAAKqC,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE3B;IAAK;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9C1C,OAAA;MAAM2C,QAAQ,EAAEnB,YAAa;MAAAc,QAAA,gBAC3BtC,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtC,OAAA;UAAO4C,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3C1C,OAAA;UACE6C,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACb9B,KAAK,EAAEb,QAAS;UAChB4C,QAAQ,EAAGtB,CAAC,IAAKrB,WAAW,CAACqB,CAAC,CAACuB,MAAM,CAAChC,KAAK,CAAE;UAC7CiC,WAAW,EAAC,0BAA0B;UACtCC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtC,OAAA;UAAAsC,QAAA,EAAO;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtBrC,OAAO,CAAC8C,GAAG,CAAC,CAACtB,MAAM,EAAEd,KAAK,kBACzBf,OAAA;UAAiBqC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACvCtC,OAAA;YACE6C,IAAI,EAAC,MAAM;YACX7B,KAAK,EAAEa,MAAO;YACdkB,QAAQ,EAAGtB,CAAC,IAAKX,kBAAkB,CAACC,KAAK,EAAEU,CAAC,CAACuB,MAAM,CAAChC,KAAK,CAAE;YAC3DiC,WAAW,EAAE,UAAUlC,KAAK,GAAG,CAAC,EAAG;YACnCmC,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDrC,OAAO,CAACc,MAAM,GAAG,CAAC,iBACjBnB,OAAA;YACE6C,IAAI,EAAC,QAAQ;YACbO,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACL,KAAK,CAAE;YAAAuB,QAAA,EACpC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA,GAfO3B,KAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBV,CACN,CAAC,EAEDrC,OAAO,CAACc,MAAM,GAAG,CAAC,iBACjBnB,OAAA;UACE6C,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,gBAAgB;UAC1Be,OAAO,EAAElC,SAAU;UAAAoB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCtC,OAAA;UAAO4C,OAAO,EAAC,eAAe;UAAAN,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtD1C,OAAA;UACE8C,EAAE,EAAC,eAAe;UAClB9B,KAAK,EAAET,aAAc;UACrBwC,QAAQ,EAAGtB,CAAC,IAAKjB,gBAAgB,CAAC6C,QAAQ,CAAC5B,CAAC,CAACuB,MAAM,CAAChC,KAAK,CAAC,CAAE;UAAAsB,QAAA,EAE3DjC,OAAO,CAAC8C,GAAG,CAAC,CAACtB,MAAM,EAAEd,KAAK,kBACzBf,OAAA;YAAoBgB,KAAK,EAAED,KAAM;YAAAuB,QAAA,GAAC,SACzB,EAACvB,KAAK,GAAG,CAAC,EAAC,IAAE,EAACc,MAAM,IAAI,UAAUd,KAAK,GAAG,CAAC,EAAE;UAAA,GADzCA,KAAK;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN1C,OAAA;QACE6C,IAAI,EAAC,QAAQ;QACbR,SAAS,EAAC,YAAY;QACtBiB,QAAQ,EAAE7C,OAAQ;QAAA6B,QAAA,EAEjB7B,OAAO,GAAG,kBAAkB,GAAG;MAAa;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxC,EAAA,CArJQD,UAAU;EAAA,QAMAH,WAAW;AAAA;AAAAyD,EAAA,GANrBtD,UAAU;AAuJnB,eAAeA,UAAU;AAAC,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}