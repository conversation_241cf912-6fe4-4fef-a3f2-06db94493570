{"name": "livepoll-quiz-server", "version": "1.0.0", "description": "Backend server for Live Polling and Quiz App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongoose": "^7.5.0", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["polling", "quiz", "realtime", "socket.io"], "author": "", "license": "MIT"}