import * as React from 'react';
import createSvgIcon from './utils/createSvgIcon';
import { jsx as _jsx } from "react/jsx-runtime";
import { jsxs as _jsxs } from "react/jsx-runtime";
export default createSvgIcon( /*#__PURE__*/_jsxs(React.Fragment, {
  children: [/*#__PURE__*/_jsx("path", {
    fillOpacity: ".3",
    d: "M2 22h20V2L2 22z"
  }), /*#__PURE__*/_jsx("path", {
    d: "M17 7L2 22h15V7z"
  })]
}), 'SignalCellular3BarSharp');