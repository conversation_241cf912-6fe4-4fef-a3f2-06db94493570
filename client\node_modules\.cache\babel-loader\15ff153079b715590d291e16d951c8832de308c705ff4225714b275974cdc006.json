{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\livepoll_and_quizapp\\\\client\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport HomePage from './pages/HomePage';\nimport AdminDashboard from './pages/AdminDashboard';\nimport ParticipantDashboard from './pages/ParticipantDashboard';\nimport CreatePoll from './components/CreatePoll';\nimport CreateQuiz from './components/CreateQuiz';\nimport PollAdmin from './pages/PollAdmin';\nimport QuizAdmin from './pages/QuizAdmin';\nimport PollParticipant from './pages/PollParticipant';\nimport QuizParticipant from './pages/QuizParticipant';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Live Poll & Quiz App\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Create engaging polls and quizzes with real-time results\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"nav-container\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"nav-button\",\n            children: \"Home\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/admin\",\n            className: \"nav-button admin\",\n            children: \"Admin Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/participate\",\n            className: \"nav-button participant\",\n            children: \"Join Session\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin\",\n          element: /*#__PURE__*/_jsxDEV(AdminDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/create-poll\",\n          element: /*#__PURE__*/_jsxDEV(CreatePoll, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/create-quiz\",\n          element: /*#__PURE__*/_jsxDEV(CreateQuiz, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/poll/:code\",\n          element: /*#__PURE__*/_jsxDEV(PollAdmin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/admin/quiz/:code\",\n          element: /*#__PURE__*/_jsxDEV(QuizAdmin, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 52\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/participate\",\n          element: /*#__PURE__*/_jsxDEV(ParticipantDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/participate/poll/:code\",\n          element: /*#__PURE__*/_jsxDEV(PollParticipant, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/participate/quiz/:code\",\n          element: /*#__PURE__*/_jsxDEV(QuizParticipant, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 58\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Link", "HomePage", "AdminDashboard", "ParticipantDashboard", "CreatePoll", "CreateQuiz", "PollAdmin", "QuizAdmin", "PollParticipant", "QuizParticipant", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';\nimport HomePage from './pages/HomePage';\nimport AdminDashboard from './pages/AdminDashboard';\nimport ParticipantDashboard from './pages/ParticipantDashboard';\nimport CreatePoll from './components/CreatePoll';\nimport CreateQuiz from './components/CreateQuiz';\nimport PollAdmin from './pages/PollAdmin';\nimport QuizAdmin from './pages/QuizAdmin';\nimport PollParticipant from './pages/PollParticipant';\nimport QuizParticipant from './pages/QuizParticipant';\n\nfunction App() {\n  return (\n    <Router>\n      <div className=\"container\">\n        <header className=\"header\">\n          <h1>Live Poll & Quiz App</h1>\n          <p>Create engaging polls and quizzes with real-time results</p>\n        </header>\n\n        <nav className=\"nav-container\">\n          <div className=\"nav-buttons\">\n            <Link to=\"/\" className=\"nav-button\">Home</Link>\n            <Link to=\"/admin\" className=\"nav-button admin\">Admin Dashboard</Link>\n            <Link to=\"/participate\" className=\"nav-button participant\">Join Session</Link>\n          </div>\n        </nav>\n\n        <Routes>\n          <Route path=\"/\" element={<HomePage />} />\n          <Route path=\"/admin\" element={<AdminDashboard />} />\n          <Route path=\"/admin/create-poll\" element={<CreatePoll />} />\n          <Route path=\"/admin/create-quiz\" element={<CreateQuiz />} />\n          <Route path=\"/admin/poll/:code\" element={<PollAdmin />} />\n          <Route path=\"/admin/quiz/:code\" element={<QuizAdmin />} />\n          <Route path=\"/participate\" element={<ParticipantDashboard />} />\n          <Route path=\"/participate/poll/:code\" element={<PollParticipant />} />\n          <Route path=\"/participate/quiz/:code\" element={<QuizParticipant />} />\n        </Routes>\n      </div>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,kBAAkB;AAC/E,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,oBAAoB,MAAM,8BAA8B;AAC/D,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,eAAe,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACd,MAAM;IAAAgB,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAD,QAAA,gBACxBF,OAAA;QAAQG,SAAS,EAAC,QAAQ;QAAAD,QAAA,gBACxBF,OAAA;UAAAE,QAAA,EAAI;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BP,OAAA;UAAAE,QAAA,EAAG;QAAwD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC,eAETP,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5BF,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAD,QAAA,gBAC1BF,OAAA,CAACX,IAAI;YAACmB,EAAE,EAAC,GAAG;YAACL,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/CP,OAAA,CAACX,IAAI;YAACmB,EAAE,EAAC,QAAQ;YAACL,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrEP,OAAA,CAACX,IAAI;YAACmB,EAAE,EAAC,cAAc;YAACL,SAAS,EAAC,wBAAwB;YAAAD,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENP,OAAA,CAACb,MAAM;QAAAe,QAAA,gBACLF,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEV,OAAA,CAACV,QAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACzCP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEV,OAAA,CAACT,cAAc;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAEV,OAAA,CAACP,UAAU;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAEV,OAAA,CAACN,UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5DP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEV,OAAA,CAACL,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,mBAAmB;UAACC,OAAO,eAAEV,OAAA,CAACJ,SAAS;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEV,OAAA,CAACR,oBAAoB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChEP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEV,OAAA,CAACH,eAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtEP,OAAA,CAACZ,KAAK;UAACqB,IAAI,EAAC,yBAAyB;UAACC,OAAO,eAAEV,OAAA,CAACF,eAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACI,EAAA,GA/BQV,GAAG;AAiCZ,eAAeA,GAAG;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}