{"ast": null, "code": "import { io } from 'socket.io-client';\nconst socket = io('http://localhost:5000');\nexport default socket;", "map": {"version": 3, "names": ["io", "socket"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/src/socket.js"], "sourcesContent": ["import { io } from 'socket.io-client';\n\nconst socket = io('http://localhost:5000');\n\nexport default socket;\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,kBAAkB;AAErC,MAAMC,MAAM,GAAGD,EAAE,CAAC,uBAAuB,CAAC;AAE1C,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}