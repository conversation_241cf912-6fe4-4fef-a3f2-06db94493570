* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header h1 {
  font-size: 3rem;
  margin: 0;
  font-weight: 700;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 1.2rem;
  margin: 10px 0 0 0;
  opacity: 0.9;
}

/* Navigation and Dashboard Styles */
.nav-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.nav-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.nav-button {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
}

.nav-button.admin {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.nav-button.participant {
  background: linear-gradient(45deg, #667eea, #764ba2);
}

.mode-selector {
  text-align: center;
  margin-bottom: 30px;
}

.mode-toggle {
  display: inline-flex;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 5px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.mode-button {
  padding: 15px 30px;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 16px;
}

.mode-button.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.mode-button:not(.active) {
  background: transparent;
  color: #666;
}

.mode-button:not(.active):hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* Form and Card Styles */
.form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.card.admin-card {
  border-left: 5px solid #4ecdc4;
}

.card.participant-card {
  border-left: 5px solid #667eea;
}

.qr-container {
  text-align: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  margin: 20px 0;
  border: 2px dashed #ddd;
}

.qr-code {
  margin: 15px 0;
}

.code-display {
  font-family: 'Courier New', monospace;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 10px 20px;
  border-radius: 10px;
  margin: 10px 0;
  letter-spacing: 2px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.options-container {
  margin-bottom: 20px;
}

.option-input {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-input input {
  flex: 1;
  margin-right: 10px;
}

.option-input button {
  padding: 8px 12px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.add-option-btn {
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 20px;
}

.correct-answer-selector {
  margin-bottom: 20px;
}

.correct-answer-selector select {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e1e1;
  border-radius: 5px;
  font-size: 16px;
}

.submit-btn {
  width: 100%;
  padding: 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background: #0056b3;
}

.submit-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.question-container {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.question-title {
  font-size: 24px;
  margin-bottom: 30px;
  color: #333;
  text-align: center;
}

.options-list {
  list-style: none;
  padding: 0;
}

.option-item {
  margin-bottom: 15px;
}

.option-button {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e1e1;
  border-radius: 5px;
  background: white;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
  text-align: left;
}

.option-button:hover {
  border-color: #007bff;
  background: #f8f9fa;
}

.option-button.selected {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.option-button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.results-container {
  background: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 20px rgba(0,0,0,0.1);
}

.results-title {
  font-size: 20px;
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

.result-item {
  margin-bottom: 15px;
}

.result-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-weight: 500;
}

.result-bar {
  height: 30px;
  background: #e1e1e1;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
}

.result-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.5s ease;
  border-radius: 15px;
}

.result-fill.correct {
  background: #28a745;
}

.result-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.feedback {
  text-align: center;
  padding: 20px;
  margin-top: 20px;
  border-radius: 5px;
  font-weight: 500;
}

.feedback.correct {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.feedback.incorrect {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.error {
  color: #dc3545;
  text-align: center;
  padding: 10px;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
}
