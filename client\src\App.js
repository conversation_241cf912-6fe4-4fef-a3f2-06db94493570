import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import HomePage from './pages/HomePage';
import AdminDashboard from './pages/AdminDashboard';
import ParticipantDashboard from './pages/ParticipantDashboard';
import CreatePoll from './components/CreatePoll';
import CreateQuiz from './components/CreateQuiz';
import PollAdmin from './pages/PollAdmin';
import QuizAdmin from './pages/QuizAdmin';
import PollParticipant from './pages/PollParticipant';
import QuizParticipant from './pages/QuizParticipant';

function App() {
  return (
    <Router>
      <div className="container">
        <header className="header">
          <h1>Live Poll & Quiz App</h1>
          <p>Create engaging polls and quizzes with real-time results</p>
        </header>

        <nav className="nav-container">
          <div className="nav-buttons">
            <Link to="/" className="nav-button">Home</Link>
            <Link to="/admin" className="nav-button admin">Admin Dashboard</Link>
            <Link to="/participate" className="nav-button participant">Join Session</Link>
          </div>
        </nav>

        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/admin" element={<AdminDashboard />} />
          <Route path="/admin/create-poll" element={<CreatePoll />} />
          <Route path="/admin/create-quiz" element={<CreateQuiz />} />
          <Route path="/admin/poll/:code" element={<PollAdmin />} />
          <Route path="/admin/quiz/:code" element={<QuizAdmin />} />
          <Route path="/participate" element={<ParticipantDashboard />} />
          <Route path="/participate/poll/:code" element={<PollParticipant />} />
          <Route path="/participate/quiz/:code" element={<QuizParticipant />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
