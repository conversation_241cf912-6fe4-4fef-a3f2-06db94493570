"use strict";
function stringifyNode(node, custom) {
    var type = node.type;
    var value = node.value;
    var buf;
    var customResult;
    if (custom && (customResult = custom(node)) !== undefined) {
        return customResult;
    } else if (type === "word" || type === "space") {
        return value;
    } else if (type === "string") {
        buf = node.quote || "";
        return buf + value + (node.unclosed ? "" : buf);
    } else if (type === "comment") {
        return "/*" + value + (node.unclosed ? "" : "*/");
    } else if (type === "div") {
        return (node.before || "") + value + (node.after || "");
    } else if (Array.isArray(node.nodes)) {
        buf = stringify(node.nodes, custom);
        if (type !== "function") {
            return buf;
        }
        return value + "(" + (node.before || "") + buf + (node.after || "") + (node.unclosed ? "" : ")");
    }
    return value;
}
function stringify(nodes, custom) {
    var result, i;
    if (Array.isArray(nodes)) {
        result = "";
        for(i = nodes.length - 1; ~i; i -= 1){
            result = stringifyNode(nodes[i], custom) + result;
        }
        return result;
    }
    return stringifyNode(nodes, custom);
}
module.exports = stringify;
