import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function ParticipantDashboard() {
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleJoinSession = async (e) => {
    e.preventDefault();
    if (!code.trim()) {
      setError('Please enter a session code');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Try to find poll first
      const pollResponse = await fetch(`/api/polls/participate/${code.toUpperCase()}`);
      if (pollResponse.ok) {
        navigate(`/participate/poll/${code.toUpperCase()}`);
        return;
      }

      // Try to find quiz
      const quizResponse = await fetch(`/api/quizzes/participate/${code.toUpperCase()}`);
      if (quizResponse.ok) {
        navigate(`/participate/quiz/${code.toUpperCase()}`);
        return;
      }

      setError('Session not found or inactive. Please check your code.');
    } catch (err) {
      setError('Failed to join session. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="participant-dashboard">
      <div className="join-section">
        <div className="join-card">
          <div className="join-header">
            <h2>Join a Live Session</h2>
            <p>Enter the session code to participate in a poll or quiz</p>
          </div>

          <form onSubmit={handleJoinSession} className="join-form">
            <div className="form-group">
              <label htmlFor="sessionCode">Session Code</label>
              <input
                type="text"
                id="sessionCode"
                value={code}
                onChange={(e) => setCode(e.target.value.toUpperCase())}
                placeholder="Enter 8-character code (e.g., ABC12345)"
                maxLength={8}
                className="code-input"
                autoComplete="off"
              />
            </div>

            {error && <div className="error">{error}</div>}

            <button 
              type="submit" 
              className="join-button"
              disabled={loading || !code.trim()}
            >
              {loading ? 'Joining...' : 'Join Session'}
            </button>
          </form>
        </div>
      </div>

      <div className="instructions-section">
        <h3>How to Join</h3>
        <div className="instructions-grid">
          <div className="instruction-card">
            <div className="instruction-number">1</div>
            <div className="instruction-content">
              <h4>Get the Code</h4>
              <p>Ask the session host for the 8-character participant code</p>
            </div>
          </div>

          <div className="instruction-card">
            <div className="instruction-number">2</div>
            <div className="instruction-content">
              <h4>Enter Code</h4>
              <p>Type the code in the input field above (case insensitive)</p>
            </div>
          </div>

          <div className="instruction-card">
            <div className="instruction-number">3</div>
            <div className="instruction-content">
              <h4>Participate</h4>
              <p>Vote in polls or answer quiz questions and see live results</p>
            </div>
          </div>

          <div className="instruction-card">
            <div className="instruction-number">4</div>
            <div className="instruction-content">
              <h4>Real-time Updates</h4>
              <p>Watch results update instantly as others participate</p>
            </div>
          </div>
        </div>
      </div>

      <div className="features-section">
        <h3>What You Can Do</h3>
        <div className="features-list">
          <div className="feature-item">
            <span className="feature-icon">📊</span>
            <div className="feature-text">
              <h4>Vote in Polls</h4>
              <p>Choose from multiple options and see how others voted</p>
            </div>
          </div>

          <div className="feature-item">
            <span className="feature-icon">🧠</span>
            <div className="feature-text">
              <h4>Answer Quizzes</h4>
              <p>Test your knowledge and get instant feedback</p>
            </div>
          </div>

          <div className="feature-item">
            <span className="feature-icon">⚡</span>
            <div className="feature-text">
              <h4>Live Results</h4>
              <p>See real-time updates as responses come in</p>
            </div>
          </div>

          <div className="feature-item">
            <span className="feature-icon">📱</span>
            <div className="feature-text">
              <h4>Mobile Friendly</h4>
              <p>Participate from any device with a web browser</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ParticipantDashboard;
