{"ast": null, "code": "import { ERROR_PACKET, PACKET_TYPES_REVERSE } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1 ? {\n    type: PACKET_TYPES_REVERSE[type],\n    data: encodedPacket.substring(1)\n  } : {\n    type: PACKET_TYPES_REVERSE[type]\n  };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n  if (withNativeArrayBuffer) {\n    const decoded = decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return {\n      base64: true,\n      data\n    }; // fallback for old browsers\n  }\n};\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      if (data instanceof Blob) {\n        // from WebSocket + binaryType \"blob\"\n        return data;\n      } else {\n        // from HTTP long-polling or WebTransport\n        return new Blob([data]);\n      }\n    case \"arraybuffer\":\n    default:\n      if (data instanceof ArrayBuffer) {\n        // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n        return data;\n      } else {\n        // from WebTransport (Uint8Array)\n        return data.buffer;\n      }\n  }\n};", "map": {"version": 3, "names": ["ERROR_PACKET", "PACKET_TYPES_REVERSE", "decode", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "decodePacket", "encodedPacket", "binaryType", "type", "data", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "packetType", "length", "decoded", "base64", "Blob", "buffer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/node_modules/engine.io-parser/build/esm/decodePacket.browser.js"], "sourcesContent": ["import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,oBAAoB,QAAS,cAAc;AAClE,SAASC,MAAM,QAAQ,iCAAiC;AACxD,MAAMC,qBAAqB,GAAG,OAAOC,WAAW,KAAK,UAAU;AAC/D,OAAO,MAAMC,YAAY,GAAGA,CAACC,aAAa,EAAEC,UAAU,KAAK;EACvD,IAAI,OAAOD,aAAa,KAAK,QAAQ,EAAE;IACnC,OAAO;MACHE,IAAI,EAAE,SAAS;MACfC,IAAI,EAAEC,SAAS,CAACJ,aAAa,EAAEC,UAAU;IAC7C,CAAC;EACL;EACA,MAAMC,IAAI,GAAGF,aAAa,CAACK,MAAM,CAAC,CAAC,CAAC;EACpC,IAAIH,IAAI,KAAK,GAAG,EAAE;IACd,OAAO;MACHA,IAAI,EAAE,SAAS;MACfC,IAAI,EAAEG,kBAAkB,CAACN,aAAa,CAACO,SAAS,CAAC,CAAC,CAAC,EAAEN,UAAU;IACnE,CAAC;EACL;EACA,MAAMO,UAAU,GAAGb,oBAAoB,CAACO,IAAI,CAAC;EAC7C,IAAI,CAACM,UAAU,EAAE;IACb,OAAOd,YAAY;EACvB;EACA,OAAOM,aAAa,CAACS,MAAM,GAAG,CAAC,GACzB;IACEP,IAAI,EAAEP,oBAAoB,CAACO,IAAI,CAAC;IAChCC,IAAI,EAAEH,aAAa,CAACO,SAAS,CAAC,CAAC;EACnC,CAAC,GACC;IACEL,IAAI,EAAEP,oBAAoB,CAACO,IAAI;EACnC,CAAC;AACT,CAAC;AACD,MAAMI,kBAAkB,GAAGA,CAACH,IAAI,EAAEF,UAAU,KAAK;EAC7C,IAAIJ,qBAAqB,EAAE;IACvB,MAAMa,OAAO,GAAGd,MAAM,CAACO,IAAI,CAAC;IAC5B,OAAOC,SAAS,CAACM,OAAO,EAAET,UAAU,CAAC;EACzC,CAAC,MACI;IACD,OAAO;MAAEU,MAAM,EAAE,IAAI;MAAER;IAAK,CAAC,CAAC,CAAC;EACnC;AACJ,CAAC;AACD,MAAMC,SAAS,GAAGA,CAACD,IAAI,EAAEF,UAAU,KAAK;EACpC,QAAQA,UAAU;IACd,KAAK,MAAM;MACP,IAAIE,IAAI,YAAYS,IAAI,EAAE;QACtB;QACA,OAAOT,IAAI;MACf,CAAC,MACI;QACD;QACA,OAAO,IAAIS,IAAI,CAAC,CAACT,IAAI,CAAC,CAAC;MAC3B;IACJ,KAAK,aAAa;IAClB;MACI,IAAIA,IAAI,YAAYL,WAAW,EAAE;QAC7B;QACA,OAAOK,IAAI;MACf,CAAC,MACI;QACD;QACA,OAAOA,IAAI,CAACU,MAAM;MACtB;EACR;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}