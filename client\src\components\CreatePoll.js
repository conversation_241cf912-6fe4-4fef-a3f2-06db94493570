import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { QRCodeSVG } from 'qrcode.react';

function CreatePoll() {
  const [question, setQuestion] = useState('');
  const [options, setOptions] = useState(['', '']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [createdPoll, setCreatedPoll] = useState(null);
  const navigate = useNavigate();

  const handleOptionChange = (index, value) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const addOption = () => {
    if (options.length < 5) {
      setOptions([...options, '']);
    }
  };

  const removeOption = (index) => {
    if (options.length > 2) {
      const newOptions = options.filter((_, i) => i !== index);
      setOptions(newOptions);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    
    // Validation
    if (!question.trim()) {
      setError('Question is required');
      return;
    }

    const validOptions = options.filter(option => option.trim() !== '');
    if (validOptions.length < 2) {
      setError('At least 2 options are required');
      return;
    }

    setLoading(true);
    
    try {
      const response = await axios.post('/api/polls', {
        question: question.trim(),
        options: validOptions
      });

      setCreatedPoll(response.data);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to create poll');
    } finally {
      setLoading(false);
    }
  };

  if (createdPoll) {
    return (
      <div className="form-container">
        <h2>✅ Poll Created Successfully!</h2>

        <div className="success-content">
          <div className="poll-info">
            <h3>{createdPoll.question}</h3>
            <p>Your poll has been created and is now active!</p>
          </div>

          <div className="codes-section">
            <div className="code-card admin-card">
              <h4>👨‍💼 Admin Access</h4>
              <div className="code-display">{createdPoll.adminCode}</div>
              <div className="qr-container">
                <QRCodeSVG
                  value={createdPoll.adminUrl}
                  size={150}
                  className="qr-code"
                />
                <p>Admin QR Code</p>
              </div>
              <button
                onClick={() => navigate(`/admin/poll/${createdPoll.adminCode}`)}
                className="nav-button admin"
              >
                Go to Admin Panel
              </button>
            </div>

            <div className="code-card participant-card">
              <h4>👥 Participant Access</h4>
              <div className="code-display">{createdPoll.participantCode}</div>
              <div className="qr-container">
                <QRCodeSVG
                  value={createdPoll.participantUrl}
                  size={150}
                  className="qr-code"
                />
                <p>Participant QR Code</p>
              </div>
              <button
                onClick={() => navigate(`/participate/poll/${createdPoll.participantCode}`)}
                className="nav-button participant"
              >
                Join as Participant
              </button>
            </div>
          </div>

          <div className="share-instructions">
            <h4>📤 How to Share</h4>
            <ul>
              <li>Share the <strong>Participant Code</strong> or QR code with your audience</li>
              <li>Use the <strong>Admin Code</strong> to manage and view results</li>
              <li>Participants can join at: <code>/participate</code></li>
            </ul>
          </div>

          <div className="action-buttons">
            <button
              onClick={() => {
                setCreatedPoll(null);
                setQuestion('');
                setOptions(['', '']);
                setError('');
              }}
              className="nav-button"
            >
              Create Another Poll
            </button>
            <button
              onClick={() => navigate('/admin')}
              className="nav-button admin"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="form-container">
      <h2>Create a Poll</h2>

      {error && <div className="error">{error}</div>}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="question">Question:</label>
          <input
            type="text"
            id="question"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder="Enter your poll question"
            required
          />
        </div>

        <div className="options-container">
          <label>Options:</label>
          {options.map((option, index) => (
            <div key={index} className="option-input">
              <input
                type="text"
                value={option}
                onChange={(e) => handleOptionChange(index, e.target.value)}
                placeholder={`Option ${index + 1}`}
                required
              />
              {options.length > 2 && (
                <button
                  type="button"
                  onClick={() => removeOption(index)}
                >
                  Remove
                </button>
              )}
            </div>
          ))}

          {options.length < 5 && (
            <button
              type="button"
              className="add-option-btn"
              onClick={addOption}
            >
              Add Option
            </button>
          )}
        </div>

        <button
          type="submit"
          className="submit-btn"
          disabled={loading}
        >
          {loading ? 'Creating Poll...' : 'Create Poll'}
        </button>
      </form>
    </div>
  );
}

export default CreatePoll;
