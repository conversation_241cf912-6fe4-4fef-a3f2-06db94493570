{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\livepoll_and_quizapp\\\\client\\\\src\\\\components\\\\CreatePoll.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { QRCodeSVG } from 'qrcode.react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CreatePoll() {\n  _s();\n  const [question, setQuestion] = useState('');\n  const [options, setOptions] = useState(['', '']);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [createdPoll, setCreatedPoll] = useState(null);\n  const navigate = useNavigate();\n  const handleOptionChange = (index, value) => {\n    const newOptions = [...options];\n    newOptions[index] = value;\n    setOptions(newOptions);\n  };\n  const addOption = () => {\n    if (options.length < 5) {\n      setOptions([...options, '']);\n    }\n  };\n  const removeOption = index => {\n    if (options.length > 2) {\n      const newOptions = options.filter((_, i) => i !== index);\n      setOptions(newOptions);\n    }\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n\n    // Validation\n    if (!question.trim()) {\n      setError('Question is required');\n      return;\n    }\n    const validOptions = options.filter(option => option.trim() !== '');\n    if (validOptions.length < 2) {\n      setError('At least 2 options are required');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post('/api/polls', {\n        question: question.trim(),\n        options: validOptions\n      });\n      navigate(`/poll/${response.data._id}`);\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Failed to create poll');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"form-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Create a Poll\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"question\",\n          children: \"Question:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"question\",\n          value: question,\n          onChange: e => setQuestion(e.target.value),\n          placeholder: \"Enter your poll question\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"options-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          children: \"Options:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"option-input\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: option,\n            onChange: e => handleOptionChange(index, e.target.value),\n            placeholder: `Option ${index + 1}`,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this), options.length > 2 && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: () => removeOption(index),\n            children: \"Remove\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 17\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)), options.length < 5 && /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"button\",\n          className: \"add-option-btn\",\n          onClick: addOption,\n          children: \"Add Option\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"submit-btn\",\n        disabled: loading,\n        children: loading ? 'Creating Poll...' : 'Create Poll'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}\n_s(CreatePoll, \"15epHWrtZoQv91dHopJrq3Dnblg=\", false, function () {\n  return [useNavigate];\n});\n_c = CreatePoll;\nexport default CreatePoll;\nvar _c;\n$RefreshReg$(_c, \"CreatePoll\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "QRCodeSVG", "jsxDEV", "_jsxDEV", "CreatePoll", "_s", "question", "setQuestion", "options", "setOptions", "loading", "setLoading", "error", "setError", "createdPoll", "set<PERSON><PERSON>d<PERSON><PERSON>", "navigate", "handleOptionChange", "index", "value", "newOptions", "addOption", "length", "removeOption", "filter", "_", "i", "handleSubmit", "e", "preventDefault", "trim", "validOptions", "option", "response", "post", "data", "_id", "err", "_err$response", "_err$response$data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "target", "placeholder", "required", "map", "onClick", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/livepoll_and_quizapp/client/src/components/CreatePoll.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from 'axios';\nimport { useNavigate } from 'react-router-dom';\nimport { QRCodeSVG } from 'qrcode.react';\n\nfunction CreatePoll() {\n  const [question, setQuestion] = useState('');\n  const [options, setOptions] = useState(['', '']);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [createdPoll, setCreatedPoll] = useState(null);\n  const navigate = useNavigate();\n\n  const handleOptionChange = (index, value) => {\n    const newOptions = [...options];\n    newOptions[index] = value;\n    setOptions(newOptions);\n  };\n\n  const addOption = () => {\n    if (options.length < 5) {\n      setOptions([...options, '']);\n    }\n  };\n\n  const removeOption = (index) => {\n    if (options.length > 2) {\n      const newOptions = options.filter((_, i) => i !== index);\n      setOptions(newOptions);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    \n    // Validation\n    if (!question.trim()) {\n      setError('Question is required');\n      return;\n    }\n\n    const validOptions = options.filter(option => option.trim() !== '');\n    if (validOptions.length < 2) {\n      setError('At least 2 options are required');\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      const response = await axios.post('/api/polls', {\n        question: question.trim(),\n        options: validOptions\n      });\n      \n      navigate(`/poll/${response.data._id}`);\n    } catch (err) {\n      setError(err.response?.data?.error || 'Failed to create poll');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"form-container\">\n      <h2>Create a Poll</h2>\n      \n      {error && <div className=\"error\">{error}</div>}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"form-group\">\n          <label htmlFor=\"question\">Question:</label>\n          <input\n            type=\"text\"\n            id=\"question\"\n            value={question}\n            onChange={(e) => setQuestion(e.target.value)}\n            placeholder=\"Enter your poll question\"\n            required\n          />\n        </div>\n\n        <div className=\"options-container\">\n          <label>Options:</label>\n          {options.map((option, index) => (\n            <div key={index} className=\"option-input\">\n              <input\n                type=\"text\"\n                value={option}\n                onChange={(e) => handleOptionChange(index, e.target.value)}\n                placeholder={`Option ${index + 1}`}\n                required\n              />\n              {options.length > 2 && (\n                <button\n                  type=\"button\"\n                  onClick={() => removeOption(index)}\n                >\n                  Remove\n                </button>\n              )}\n            </div>\n          ))}\n          \n          {options.length < 5 && (\n            <button\n              type=\"button\"\n              className=\"add-option-btn\"\n              onClick={addOption}\n            >\n              Add Option\n            </button>\n          )}\n        </div>\n\n        <button \n          type=\"submit\" \n          className=\"submit-btn\"\n          disabled={loading}\n        >\n          {loading ? 'Creating Poll...' : 'Create Poll'}\n        </button>\n      </form>\n    </div>\n  );\n}\n\nexport default CreatePoll;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EAChD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMkB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC3C,MAAMC,UAAU,GAAG,CAAC,GAAGZ,OAAO,CAAC;IAC/BY,UAAU,CAACF,KAAK,CAAC,GAAGC,KAAK;IACzBV,UAAU,CAACW,UAAU,CAAC;EACxB,CAAC;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB,IAAIb,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACtBb,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE,EAAE,CAAC,CAAC;IAC9B;EACF,CAAC;EAED,MAAMe,YAAY,GAAIL,KAAK,IAAK;IAC9B,IAAIV,OAAO,CAACc,MAAM,GAAG,CAAC,EAAE;MACtB,MAAMF,UAAU,GAAGZ,OAAO,CAACgB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKR,KAAK,CAAC;MACxDT,UAAU,CAACW,UAAU,CAAC;IACxB;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBhB,QAAQ,CAAC,EAAE,CAAC;;IAEZ;IACA,IAAI,CAACP,QAAQ,CAACwB,IAAI,CAAC,CAAC,EAAE;MACpBjB,QAAQ,CAAC,sBAAsB,CAAC;MAChC;IACF;IAEA,MAAMkB,YAAY,GAAGvB,OAAO,CAACgB,MAAM,CAACQ,MAAM,IAAIA,MAAM,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IACnE,IAAIC,YAAY,CAACT,MAAM,GAAG,CAAC,EAAE;MAC3BT,QAAQ,CAAC,iCAAiC,CAAC;MAC3C;IACF;IAEAF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMlC,KAAK,CAACmC,IAAI,CAAC,YAAY,EAAE;QAC9C5B,QAAQ,EAAEA,QAAQ,CAACwB,IAAI,CAAC,CAAC;QACzBtB,OAAO,EAAEuB;MACX,CAAC,CAAC;MAEFf,QAAQ,CAAC,SAASiB,QAAQ,CAACE,IAAI,CAACC,GAAG,EAAE,CAAC;IACxC,CAAC,CAAC,OAAOC,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZ1B,QAAQ,CAAC,EAAAyB,aAAA,GAAAD,GAAG,CAACJ,QAAQ,cAAAK,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcH,IAAI,cAAAI,kBAAA,uBAAlBA,kBAAA,CAAoB3B,KAAK,KAAI,uBAAuB,CAAC;IAChE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAKqC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BtC,OAAA;MAAAsC,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAErBjC,KAAK,iBAAIT,OAAA;MAAKqC,SAAS,EAAC,OAAO;MAAAC,QAAA,EAAE7B;IAAK;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAE9C1C,OAAA;MAAM2C,QAAQ,EAAEnB,YAAa;MAAAc,QAAA,gBAC3BtC,OAAA;QAAKqC,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBtC,OAAA;UAAO4C,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3C1C,OAAA;UACE6C,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACb9B,KAAK,EAAEb,QAAS;UAChB4C,QAAQ,EAAGtB,CAAC,IAAKrB,WAAW,CAACqB,CAAC,CAACuB,MAAM,CAAChC,KAAK,CAAE;UAC7CiC,WAAW,EAAC,0BAA0B;UACtCC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCtC,OAAA;UAAAsC,QAAA,EAAO;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EACtBrC,OAAO,CAAC8C,GAAG,CAAC,CAACtB,MAAM,EAAEd,KAAK,kBACzBf,OAAA;UAAiBqC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACvCtC,OAAA;YACE6C,IAAI,EAAC,MAAM;YACX7B,KAAK,EAAEa,MAAO;YACdkB,QAAQ,EAAGtB,CAAC,IAAKX,kBAAkB,CAACC,KAAK,EAAEU,CAAC,CAACuB,MAAM,CAAChC,KAAK,CAAE;YAC3DiC,WAAW,EAAE,UAAUlC,KAAK,GAAG,CAAC,EAAG;YACnCmC,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EACDrC,OAAO,CAACc,MAAM,GAAG,CAAC,iBACjBnB,OAAA;YACE6C,IAAI,EAAC,QAAQ;YACbO,OAAO,EAAEA,CAAA,KAAMhC,YAAY,CAACL,KAAK,CAAE;YAAAuB,QAAA,EACpC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA,GAfO3B,KAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBV,CACN,CAAC,EAEDrC,OAAO,CAACc,MAAM,GAAG,CAAC,iBACjBnB,OAAA;UACE6C,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,gBAAgB;UAC1Be,OAAO,EAAElC,SAAU;UAAAoB,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN1C,OAAA;QACE6C,IAAI,EAAC,QAAQ;QACbR,SAAS,EAAC,YAAY;QACtBgB,QAAQ,EAAE9C,OAAQ;QAAA+B,QAAA,EAEjB/B,OAAO,GAAG,kBAAkB,GAAG;MAAa;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV;AAACxC,EAAA,CAzHQD,UAAU;EAAA,QAMAJ,WAAW;AAAA;AAAAyD,EAAA,GANrBrD,UAAU;AA2HnB,eAAeA,UAAU;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}